/*
 * 仙侠MUD专用样式
 * 参考侠客行、西游记等经典MUD界面设计
 */

/* 整体主题 - 古典仙侠风格 */
body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #e8e8e8;
    font-family: 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS', sans-serif;
    font-size: 14px;
    line-height: 1.6;
}

/* 主容器 */
#clientwrapper {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #8B4513;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(139, 69, 19, 0.5);
}

/* 消息窗口 - 主要游戏区域 */
#messagewindow {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #CD853F;
    border-radius: 4px;
    padding: 10px;
    color: #F5DEB3;
    font-family: 'Courier New', monospace;
    line-height: 1.8;
}

/* 输入区域 */
.inputfield {
    background: rgba(20, 20, 40, 0.9) !important;
    border: 2px solid #DAA520 !important;
    border-radius: 4px !important;
    color: #FFD700 !important;
    font-size: 14px !important;
    padding: 8px !important;
    font-family: 'Microsoft YaHei', sans-serif !important;
}

.inputfield:focus {
    border-color: #FF6347 !important;
    box-shadow: 0 0 10px rgba(255, 99, 71, 0.5) !important;
    outline: none !important;
}

/* 仙侠状态面板 */
.xianxia-stats {
    background: linear-gradient(145deg, #2c1810, #3d2317);
    border: 2px solid #8B4513;
    border-radius: 8px;
    padding: 15px;
    margin: 10px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.xianxia-stats h3 {
    color: #FFD700;
    text-align: center;
    margin: 0 0 15px 0;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid #DAA520;
    padding-bottom: 5px;
}

/* 属性条 */
.stat-bar {
    margin: 8px 0;
    position: relative;
}

.stat-label {
    color: #F5DEB3;
    font-size: 12px;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
}

.progress-bar {
    width: 100%;
    height: 18px;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid #8B4513;
    border-radius: 9px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
    position: relative;
}

/* 不同类型的进度条颜色 */
.spiritual-power .progress-fill {
    background: linear-gradient(90deg, #4169E1, #87CEEB);
    box-shadow: 0 0 10px rgba(65, 105, 225, 0.5);
}

.cultivation-progress .progress-fill {
    background: linear-gradient(90deg, #FFD700, #FFA500);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.health-bar .progress-fill {
    background: linear-gradient(90deg, #DC143C, #FF6347);
    box-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
}

/* 境界显示 */
.realm-display {
    text-align: center;
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid #DAA520;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
}

.realm-name {
    color: #FFD700;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.realm-level {
    color: #F5DEB3;
    font-size: 14px;
    margin-top: 5px;
}

/* 门派信息 */
.sect-info {
    background: rgba(139, 69, 19, 0.2);
    border: 1px solid #8B4513;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
}

.sect-name {
    color: #FF6347;
    font-weight: bold;
    font-size: 14px;
}

.sect-rank {
    color: #F5DEB3;
    font-size: 12px;
    margin-top: 3px;
}

/* 快捷命令栏 */
.command-hotbar {
    background: rgba(20, 20, 40, 0.9);
    border: 2px solid #8B4513;
    border-radius: 6px;
    padding: 10px;
    margin: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.hotkey-button {
    background: linear-gradient(145deg, #3d2317, #2c1810);
    border: 1px solid #DAA520;
    border-radius: 4px;
    color: #FFD700;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    min-width: 60px;
    text-align: center;
}

.hotkey-button:hover {
    background: linear-gradient(145deg, #4d2817, #3c2010);
    border-color: #FF6347;
    box-shadow: 0 0 8px rgba(255, 99, 71, 0.3);
    transform: translateY(-1px);
}

.hotkey-button:active {
    transform: translateY(0);
    box-shadow: 0 0 4px rgba(255, 99, 71, 0.5);
}

/* 聊天频道标签 */
.chat-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.6);
    border-bottom: 2px solid #8B4513;
    margin-bottom: 5px;
}

.chat-tab {
    background: rgba(139, 69, 19, 0.3);
    border: 1px solid #8B4513;
    border-bottom: none;
    color: #F5DEB3;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.chat-tab:hover {
    background: rgba(139, 69, 19, 0.5);
    color: #FFD700;
}

.chat-tab.active {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border-color: #DAA520;
}

/* 系统消息样式 */
.system-msg {
    color: #87CEEB;
    font-style: italic;
}

.cultivation-msg {
    color: #FFD700;
    font-weight: bold;
}

.breakthrough-msg {
    color: #FF6347;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.sect-msg {
    color: #98FB98;
}

.combat-msg {
    color: #FF4500;
    font-weight: bold;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #8B4513, #A0522D);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #A0522D, #CD853F);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    .xianxia-stats {
        padding: 10px;
        margin: 5px;
    }
    
    .command-hotbar {
        padding: 8px;
        margin: 5px;
    }
    
    .hotkey-button {
        padding: 6px 8px;
        font-size: 11px;
        min-width: 50px;
    }
    
    body {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
}

.breakthrough-glow {
    animation: glow 2s infinite;
}

/* 特殊效果 */
.spiritual-aura {
    position: relative;
}

.spiritual-aura::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(135, 206, 235, 0.3), transparent);
    border-radius: 8px;
    z-index: -1;
}

/* GoldenLayout 标签页样式覆盖 */
.lm_header {
    background: linear-gradient(145deg, #2c1810, #3d2317) !important;
    border-bottom: 2px solid #8B4513 !important;
}

.lm_tab {
    background: rgba(139, 69, 19, 0.3) !important;
    border: 1px solid #8B4513 !important;
    color: #F5DEB3 !important;
}

.lm_tab.lm_active {
    background: rgba(255, 215, 0, 0.2) !important;
    color: #FFD700 !important;
    border-color: #DAA520 !important;
}

.lm_content {
    background: rgba(0, 0, 0, 0.8) !important;
}
