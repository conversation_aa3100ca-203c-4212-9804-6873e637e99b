"""
仙侠道具系统
基于Evennia官方Object系统扩展的仙侠道具类

功能包括：
- 法宝系统 (品级、认主、升级)
- 仙侠武器 (剑、刀、枪、棍等)
- 丹药系统 (疗伤、恢复、增益)
- 灵石货币系统
- 符箓和阵法道具
"""

from evennia import DefaultObject, AttributeProperty
from evennia.contrib.tutorials.evadventure.objects import (
    EvAdventureObject, EvAdventureWeapon, EvAdventureConsumable
)
from evennia.contrib.tutorials.evadventure.enums import WieldLocation, ObjType, Ability
from enum import Enum
import random


# =============================================================================
# 仙侠道具枚举
# =============================================================================

class TreasureGrade(Enum):
    """法宝品级"""
    MORTAL = "凡品"
    SPIRITUAL = "灵品"
    TREASURE = "宝品"
    IMMORTAL = "仙品"
    DIVINE = "神品"


class WeaponType(Enum):
    """武器类型"""
    SWORD = "剑"
    SABER = "刀"
    SPEAR = "枪"
    STAFF = "棍"
    WHIP = "鞭"
    FAN = "扇"
    BELL = "铃"
    CAULDRON = "鼎"


class PillType(Enum):
    """丹药类型"""
    HEALING = "疗伤丹"
    SPIRITUAL = "回灵丹"
    BREAKTHROUGH = "破境丹"
    DETOX = "解毒丹"
    STRENGTH = "力量丹"
    SPEED = "速度丹"
    DEFENSE = "防御丹"


class SpiritStoneGrade(Enum):
    """灵石品级"""
    LOW = "下品"
    MEDIUM = "中品"
    HIGH = "上品"
    TOP = "极品"


# =============================================================================
# 仙侠法宝基类
# =============================================================================

class XianxiaTreasure(EvAdventureObject):
    """
    仙侠法宝基类
    所有仙侠法宝的基础类，包含认主、升级等核心功能
    """
    
    obj_type = ObjType.MAGIC
    
    # 法宝基础属性
    treasure_grade = AttributeProperty(TreasureGrade.MORTAL.value, autocreate=True)
    treasure_level = AttributeProperty(1, autocreate=True)
    
    # 灵力属性
    spiritual_power = AttributeProperty(100, autocreate=True)
    max_spiritual_power = AttributeProperty(100, autocreate=True)
    
    # 认主系统
    owner_id = AttributeProperty(None, autocreate=True)
    recognition_level = AttributeProperty(0, autocreate=True)  # 0-10
    
    # 法宝技能
    treasure_skills = AttributeProperty([], autocreate=True)
    
    # 升级材料需求
    upgrade_materials = AttributeProperty({}, autocreate=True)
    
    def at_object_creation(self):
        """法宝创建时初始化"""
        super().at_object_creation()
        
        # 根据品级设置基础属性
        self.initialize_by_grade()
        
        # 添加法宝标签
        self.tags.add("xianxia_treasure", category="item_type")
        self.tags.add(self.treasure_grade, category="treasure_grade")
        
    def initialize_by_grade(self):
        """根据品级初始化属性"""
        grade_stats = {
            TreasureGrade.MORTAL.value: {"power": 100, "skills": 1},
            TreasureGrade.SPIRITUAL.value: {"power": 300, "skills": 2},
            TreasureGrade.TREASURE.value: {"power": 800, "skills": 3},
            TreasureGrade.IMMORTAL.value: {"power": 2000, "skills": 4},
            TreasureGrade.DIVINE.value: {"power": 5000, "skills": 5}
        }
        
        stats = grade_stats.get(self.treasure_grade, grade_stats[TreasureGrade.MORTAL.value])
        self.max_spiritual_power = stats["power"]
        self.spiritual_power = self.max_spiritual_power
        
    def can_recognize_master(self, character):
        """检查是否可以认主"""
        # 已有主人
        if self.owner_id and self.owner_id != character.id:
            return False, "此法宝已有主人"
            
        # 修为不足
        if hasattr(character, 'realm_level'):
            if character.realm_level < self.treasure_level:
                return False, "修为不足，无法驾驭此法宝"
                
        return True, "可以认主"
        
    def recognize_master(self, character):
        """认主过程"""
        can_recognize, reason = self.can_recognize_master(character)
        if not can_recognize:
            character.msg(f"认主失败：{reason}")
            return False
            
        self.owner_id = character.id
        self.recognition_level = 1
        
        character.msg(f"成功认主{self.name}！")
        character.msg(f"法宝品级：{self.treasure_grade}")
        character.msg(f"认主程度：{self.recognition_level}/10")
        
        return True
        
    def improve_recognition(self, character):
        """提升认主程度"""
        if self.owner_id != character.id:
            character.msg("只有法宝主人才能提升认主程度。")
            return False
            
        if self.recognition_level >= 10:
            character.msg("认主程度已达到最高。")
            return False
            
        # 需要消耗灵力和时间
        cost = self.recognition_level * 50
        if not character.consume_spiritual_power(cost):
            character.msg(f"灵力不足，需要{cost}点灵力。")
            return False
            
        # 成功率基于角色修为
        success_rate = min(90, character.realm_level * 10 + 10)
        if random.randint(1, 100) <= success_rate:
            self.recognition_level += 1
            character.msg(f"认主程度提升到{self.recognition_level}/10！")
            
            # 认主程度提升时解锁新能力
            if self.recognition_level == 5:
                character.msg("法宝与你心意相通，战斗时将获得额外加成！")
            elif self.recognition_level == 10:
                character.msg("法宝完全认主，可以尝试升级法宝品级！")
                
            return True
        else:
            character.msg("认主失败，但认主程度略有提升。")
            return False
            
    def can_upgrade(self):
        """检查是否可以升级"""
        if self.recognition_level < 10:
            return False, "认主程度不足，需要完全认主"
            
        if self.treasure_level >= 9 and self.treasure_grade != TreasureGrade.DIVINE.value:
            return False, "需要先提升法宝品级"
            
        return True, "可以升级"
        
    def upgrade_level(self, materials=None):
        """升级法宝等级"""
        can_upgrade, reason = self.can_upgrade()
        if not can_upgrade:
            return False, reason
            
        # 检查材料 (简化版本)
        if materials:
            # 这里应该检查材料是否足够
            pass
            
        self.treasure_level += 1
        self.max_spiritual_power += 100
        self.spiritual_power = self.max_spiritual_power
        
        return True, f"法宝升级到{self.treasure_level}级！"
        
    def upgrade_grade(self):
        """提升法宝品级"""
        if self.treasure_level < 9:
            return False, "法宝等级不足，需要9级才能提升品级"
            
        grade_progression = {
            TreasureGrade.MORTAL.value: TreasureGrade.SPIRITUAL.value,
            TreasureGrade.SPIRITUAL.value: TreasureGrade.TREASURE.value,
            TreasureGrade.TREASURE.value: TreasureGrade.IMMORTAL.value,
            TreasureGrade.IMMORTAL.value: TreasureGrade.DIVINE.value
        }
        
        new_grade = grade_progression.get(self.treasure_grade)
        if not new_grade:
            return False, "法宝品级已达到最高"
            
        self.treasure_grade = new_grade
        self.treasure_level = 1  # 重置等级
        self.initialize_by_grade()
        
        return True, f"法宝品级提升到{new_grade}！"
        
    def get_power_bonus(self):
        """获取法宝威力加成"""
        base_bonus = self.treasure_level * 0.1
        recognition_bonus = self.recognition_level * 0.05
        
        grade_multiplier = {
            TreasureGrade.MORTAL.value: 1.0,
            TreasureGrade.SPIRITUAL.value: 1.5,
            TreasureGrade.TREASURE.value: 2.0,
            TreasureGrade.IMMORTAL.value: 3.0,
            TreasureGrade.DIVINE.value: 5.0
        }.get(self.treasure_grade, 1.0)
        
        return (base_bonus + recognition_bonus) * grade_multiplier
        
    def get_display_header(self, looker, **kwargs):
        """显示法宝信息"""
        header = super().get_display_header(looker, **kwargs)
        
        treasure_info = f"\n品级：{self.treasure_grade} | 等级：{self.treasure_level}"
        if self.owner_id:
            treasure_info += f" | 认主程度：{self.recognition_level}/10"
            
        return header + treasure_info


# =============================================================================
# 仙侠武器系统
# =============================================================================

class XianxiaWeapon(XianxiaTreasure, EvAdventureWeapon):
    """
    仙侠武器类
    结合法宝系统和武器系统的仙侠武器
    """
    
    obj_type = (ObjType.WEAPON, ObjType.MAGIC)
    
    # 武器特殊属性
    weapon_type = AttributeProperty(WeaponType.SWORD.value, autocreate=True)
    sharpness = AttributeProperty(1.0, autocreate=True)  # 锋利度
    durability = AttributeProperty(100, autocreate=True)  # 耐久度
    max_durability = AttributeProperty(100, autocreate=True)
    
    # 元素属性
    element_type = AttributeProperty("无", autocreate=True)  # 金木水火土
    element_power = AttributeProperty(0, autocreate=True)
    
    def at_object_creation(self):
        """武器创建时初始化"""
        super().at_object_creation()
        
        # 设置武器基础属性
        self.attack_type = Ability.STR
        self.defense_type = Ability.ARMOR
        self.damage_roll = "1d8"
        
        # 根据武器类型设置属性
        self.set_weapon_properties()
        
    def set_weapon_properties(self):
        """根据武器类型设置属性"""
        weapon_properties = {
            WeaponType.SWORD.value: {
                "inventory_slot": WieldLocation.WEAPON_HAND,
                "damage": "1d8",
                "attack_type": Ability.STR
            },
            WeaponType.SABER.value: {
                "inventory_slot": WieldLocation.WEAPON_HAND,
                "damage": "1d8",
                "attack_type": Ability.STR
            },
            WeaponType.SPEAR.value: {
                "inventory_slot": WieldLocation.TWO_HANDS,
                "damage": "1d10",
                "attack_type": Ability.STR
            },
            WeaponType.STAFF.value: {
                "inventory_slot": WieldLocation.TWO_HANDS,
                "damage": "1d6",
                "attack_type": Ability.INT
            }
        }
        
        props = weapon_properties.get(self.weapon_type, weapon_properties[WeaponType.SWORD.value])
        self.inventory_use_slot = props["inventory_slot"]
        self.damage_roll = props["damage"]
        self.attack_type = props["attack_type"]
        
    def calculate_damage_bonus(self, attacker):
        """计算武器伤害加成"""
        base_bonus = 0
        
        # 法宝威力加成
        if self.owner_id == attacker.id:
            base_bonus += self.get_power_bonus()
            
        # 锋利度加成
        base_bonus += self.sharpness
        
        # 元素加成 (如果攻击者有对应元素修炼)
        if self.element_type != "无":
            # 这里可以检查攻击者的元素亲和度
            base_bonus += self.element_power * 0.1
            
        return base_bonus
        
    def use(self, attacker, target, *args, **kwargs):
        """使用武器攻击"""
        # 检查是否为主人
        if self.owner_id and self.owner_id != attacker.id:
            attacker.msg("这不是你的法宝，无法发挥真正威力。")
            
        # 消耗耐久度
        if self.durability > 0:
            self.durability -= 1
            if self.durability <= 0:
                attacker.msg(f"{self.name}的耐久度耗尽，需要修复！")
                
        # 调用父类攻击逻辑
        return super().use(attacker, target, *args, **kwargs)
        
    def repair(self, materials=None):
        """修复武器"""
        if self.durability >= self.max_durability:
            return False, "武器无需修复"
            
        # 简化修复逻辑
        repair_amount = min(20, self.max_durability - self.durability)
        self.durability += repair_amount
        
        return True, f"修复了{repair_amount}点耐久度"


# =============================================================================
# 丹药系统
# =============================================================================

class XianxiaPill(EvAdventureConsumable):
    """
    仙侠丹药类
    各种功效的丹药
    """
    
    obj_type = ObjType.CONSUMABLE
    
    # 丹药属性
    pill_type = AttributeProperty(PillType.HEALING.value, autocreate=True)
    pill_grade = AttributeProperty("下品", autocreate=True)  # 下品/中品/上品/极品
    
    # 丹药效果
    healing_power = AttributeProperty(50, autocreate=True)
    spiritual_recovery = AttributeProperty(0, autocreate=True)
    buff_effects = AttributeProperty({}, autocreate=True)
    duration = AttributeProperty(0, autocreate=True)  # 效果持续时间(分钟)
    
    # 副作用
    side_effects = AttributeProperty([], autocreate=True)
    toxicity = AttributeProperty(0, autocreate=True)  # 毒性
    
    def at_object_creation(self):
        """丹药创建时初始化"""
        super().at_object_creation()
        
        # 根据类型设置效果
        self.set_pill_effects()
        
        # 添加丹药标签
        self.tags.add("xianxia_pill", category="item_type")
        self.tags.add(self.pill_type, category="pill_type")
        
    def set_pill_effects(self):
        """根据丹药类型设置效果"""
        pill_effects = {
            PillType.HEALING.value: {
                "healing": 100,
                "spiritual": 0,
                "toxicity": 1
            },
            PillType.SPIRITUAL.value: {
                "healing": 0,
                "spiritual": 150,
                "toxicity": 1
            },
            PillType.BREAKTHROUGH.value: {
                "healing": 0,
                "spiritual": 0,
                "buff": {"breakthrough_chance": 20},
                "toxicity": 5
            },
            PillType.STRENGTH.value: {
                "healing": 0,
                "spiritual": 0,
                "buff": {"strength": 2},
                "duration": 60,
                "toxicity": 2
            }
        }
        
        effects = pill_effects.get(self.pill_type, pill_effects[PillType.HEALING.value])
        
        self.healing_power = effects.get("healing", 0)
        self.spiritual_recovery = effects.get("spiritual", 0)
        self.buff_effects = effects.get("buff", {})
        self.duration = effects.get("duration", 0)
        self.toxicity = effects.get("toxicity", 1)
        
        # 根据品级调整效果
        grade_multiplier = {
            "下品": 1.0,
            "中品": 1.5,
            "上品": 2.0,
            "极品": 3.0
        }.get(self.pill_grade, 1.0)
        
        self.healing_power = int(self.healing_power * grade_multiplier)
        self.spiritual_recovery = int(self.spiritual_recovery * grade_multiplier)
        
    def use(self, user, *args, **kwargs):
        """使用丹药"""
        if not self.check_compatibility(user):
            return False
            
        # 应用丹药效果
        self.apply_effects(user)
        
        # 检查副作用
        self.check_side_effects(user)
        
        return True
        
    def check_compatibility(self, user):
        """检查丹药兼容性"""
        # 检查用户是否有丹毒积累
        if hasattr(user, 'pill_toxicity'):
            if user.pill_toxicity + self.toxicity > 100:
                user.msg("体内丹毒过重，无法服用更多丹药！")
                return False
                
        return True
        
    def apply_effects(self, user):
        """应用丹药效果"""
        effects_applied = []
        
        # 恢复生命值
        if self.healing_power > 0:
            if hasattr(user, 'heal'):
                user.heal(self.healing_power)
                effects_applied.append(f"恢复{self.healing_power}点生命值")
                
        # 恢复灵力
        if self.spiritual_recovery > 0:
            if hasattr(user, 'restore_spiritual_power'):
                user.restore_spiritual_power(self.spiritual_recovery)
                effects_applied.append(f"恢复{self.spiritual_recovery}点灵力")
                
        # 应用增益效果
        if self.buff_effects:
            for buff_name, buff_value in self.buff_effects.items():
                # 这里应该实现buff系统
                effects_applied.append(f"获得{buff_name}效果")
                
        # 增加丹毒
        if hasattr(user, 'pill_toxicity'):
            user.pill_toxicity = getattr(user, 'pill_toxicity', 0) + self.toxicity
        else:
            user.db.pill_toxicity = self.toxicity
            
        if effects_applied:
            user.msg(f"服用{self.name}：" + "，".join(effects_applied))
            
    def check_side_effects(self, user):
        """检查副作用"""
        if self.side_effects and random.random() < 0.1:  # 10%概率
            effect = random.choice(self.side_effects)
            user.msg(f"丹药副作用：{effect}")


# =============================================================================
# 灵石货币系统
# =============================================================================

class SpiritStone(EvAdventureObject):
    """
    灵石货币
    仙侠世界的主要货币
    """
    
    obj_type = ObjType.TREASURE
    
    stone_grade = AttributeProperty(SpiritStoneGrade.LOW.value, autocreate=True)
    spiritual_energy = AttributeProperty(1, autocreate=True)
    
    # 灵石等级对应的价值
    GRADE_VALUES = {
        SpiritStoneGrade.LOW.value: 1,
        SpiritStoneGrade.MEDIUM.value: 10,
        SpiritStoneGrade.HIGH.value: 100,
        SpiritStoneGrade.TOP.value: 1000
    }
    
    def at_object_creation(self):
        super().at_object_creation()
        
        # 设置灵石价值
        self.spiritual_energy = self.GRADE_VALUES.get(
            self.stone_grade, 
            self.GRADE_VALUES[SpiritStoneGrade.LOW.value]
        )
        
        # 设置名称
        self.key = f"{self.stone_grade}灵石"
        
        # 添加标签
        self.tags.add("spirit_stone", category="item_type")
        self.tags.add(self.stone_grade, category="stone_grade")
        
    def absorb_energy(self, character):
        """吸收灵石能量"""
        if not hasattr(character, 'spiritual_power'):
            character.msg("你无法吸收灵石的能量。")
            return False
            
        if character.spiritual_power >= character.spiritual_power_max:
            character.msg("你的灵力已满，无法吸收更多灵气。")
            return False
            
        absorbed = min(
            self.spiritual_energy,
            character.spiritual_power_max - character.spiritual_power
        )
        
        character.restore_spiritual_power(absorbed)
        character.msg(f"吸收了{absorbed}点灵气。")
        
        # 消耗灵石
        self.delete()
        return True
        
    def get_value(self):
        """获取灵石价值"""
        return self.spiritual_energy
        
    @classmethod
    def create_spirit_stone(cls, grade=SpiritStoneGrade.LOW.value, location=None):
        """创建灵石的便捷方法"""
        from evennia import create_object
        
        stone = create_object(
            cls,
            key=f"{grade}灵石",
            location=location,
            attributes=[("stone_grade", grade)]
        )
        
        return stone


# =============================================================================
# 符箓系统
# =============================================================================

class XianxiaTalisman(EvAdventureConsumable):
    """
    仙侠符箓类
    一次性使用的法术道具
    """

    obj_type = ObjType.CONSUMABLE

    # 符箓属性
    talisman_type = AttributeProperty("攻击符", autocreate=True)
    spell_power = AttributeProperty(100, autocreate=True)
    target_type = AttributeProperty("single", autocreate=True)  # single/area/self

    def at_object_creation(self):
        super().at_object_creation()
        self.tags.add("xianxia_talisman", category="item_type")

    def use(self, user, target=None, *args, **kwargs):
        """使用符箓"""
        if self.target_type == "single" and not target:
            user.msg("这个符箓需要指定目标。")
            return False

        # 应用符箓效果
        self.apply_talisman_effect(user, target)

        # 消耗符箓
        user.msg(f"使用了{self.name}！")
        return True

    def apply_talisman_effect(self, user, target):
        """应用符箓效果"""
        if self.talisman_type == "攻击符":
            if target:
                # 造成伤害
                damage = self.spell_power
                target.msg(f"被{self.name}击中，受到{damage}点伤害！")
                user.msg(f"{self.name}击中{target.name}！")
        elif self.talisman_type == "治疗符":
            # 恢复生命值
            if hasattr(user, 'heal'):
                user.heal(self.spell_power)
                user.msg(f"{self.name}恢复了你{self.spell_power}点生命值。")
