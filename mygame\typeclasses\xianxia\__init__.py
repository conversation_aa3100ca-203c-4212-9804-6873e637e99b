"""
仙侠MUD系统初始化模块

这个模块包含了仙侠MUD的所有核心组件：
- 角色系统 (characters.py)
- 道具系统 (objects.py) 
- 房间系统 (rooms.py)
- 命令系统 (commands.py)

使用方法：
1. 将xianxia文件夹放入Evennia项目的typeclasses目录
2. 在settings.py中配置使用仙侠类型类
3. 运行测试脚本验证功能
"""

# 导入所有核心类
from .characters import XianxiaCharacter, Realm, Sect, XianxiaSkill
from .objects import (
    XianxiaTreasure, XianxiaWeapon, XianxiaPill, SpiritStone, XianxiaTalisman,
    TreasureGrade, WeaponType, PillType, SpiritStoneGrade
)
from .rooms import (
    XianxiaRoom, XianxiaDungeonRoom, CultivationRoom, AlchemyRoom, 
    TreasureVault, SectHall, Wilderness,
    RoomType, SpiritualDensity, FengShuiType
)
from .commands import XianxiaCmdSet

# 版本信息
__version__ = "1.0.0"
__author__ = "Xianxia MUD Development Team"

# 导出的主要类
__all__ = [
    # 角色系统
    'XianxiaCharacter', 'Realm', 'Sect', 'XianxiaSkill',
    
    # 道具系统
    'XianxiaTreasure', 'XianxiaWeapon', 'XianxiaPill', 'SpiritStone', 'XianxiaTalisman',
    'TreasureGrade', 'WeaponType', 'PillType', 'SpiritStoneGrade',
    
    # 房间系统
    'XianxiaRoom', 'XianxiaDungeonRoom', 'CultivationRoom', 'AlchemyRoom',
    'TreasureVault', 'SectHall', 'Wilderness',
    'RoomType', 'SpiritualDensity', 'FengShuiType',
    
    # 命令系统
    'XianxiaCmdSet'
]

# 系统配置
XIANXIA_CONFIG = {
    'default_realm': Realm.QI_REFINING.value,
    'default_sect': Sect.NONE.value,
    'max_realm_level': 9,
    'spiritual_power_regen_rate': 1,  # 每分钟恢复点数
    'cultivation_base_exp': 1000,
    'breakthrough_base_risk': 20,  # 基础突破风险20%
}

def get_config(key, default=None):
    """获取配置值"""
    return XIANXIA_CONFIG.get(key, default)

def set_config(key, value):
    """设置配置值"""
    XIANXIA_CONFIG[key] = value
