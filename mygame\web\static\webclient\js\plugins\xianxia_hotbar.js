/*
 * 仙侠快捷命令栏插件
 * 提供常用仙侠命令的快捷按钮
 */

let xianxia_hotbar_plugin = (function () {
    
    // 预定义的快捷命令
    const defaultCommands = [
        { name: "状态", command: "状态", tooltip: "查看角色状态" },
        { name: "修炼", command: "修炼", tooltip: "开始修炼" },
        { name: "突破", command: "突破", tooltip: "尝试突破境界" },
        { name: "技能", command: "技能", tooltip: "查看技能列表" },
        { name: "法宝", command: "法宝", tooltip: "查看法宝" },
        { name: "门派", command: "加入门派", tooltip: "门派相关操作" },
        { name: "背包", command: "inventory", tooltip: "查看背包" },
        { name: "看", command: "look", tooltip: "查看周围" },
        { name: "帮助", command: "help", tooltip: "查看帮助" }
    ];
    
    // 用户自定义命令（从localStorage加载）
    let customCommands = [];
    
    // 创建快捷栏HTML
    function createHotbar() {
        let html = `
            <div class="command-hotbar">
                <div style="width: 100%; margin-bottom: 10px; text-align: center; color: #FFD700; font-weight: bold; border-bottom: 1px solid #8B4513; padding-bottom: 5px;">
                    快捷命令
                </div>
                <div class="hotbar-buttons" id="hotbar-buttons">
        `;
        
        // 添加默认命令按钮
        defaultCommands.forEach((cmd, index) => {
            html += `
                <div class="hotkey-button" 
                     data-command="${cmd.command}" 
                     title="${cmd.tooltip}"
                     onclick="xianxia_hotbar_plugin.executeCommand('${cmd.command}')">
                    ${cmd.name}
                </div>
            `;
        });
        
        html += `
                </div>
                <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #8B4513;">
                    <div style="display: flex; gap: 5px; align-items: center;">
                        <input type="text" id="custom-command" placeholder="自定义命令" 
                               style="flex: 1; background: rgba(0,0,0,0.6); border: 1px solid #8B4513; 
                                      color: #FFD700; padding: 4px; border-radius: 3px; font-size: 12px;">
                        <button onclick="xianxia_hotbar_plugin.addCustomCommand()" 
                                style="background: #8B4513; border: 1px solid #DAA520; color: #FFD700; 
                                       padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px;">
                            添加
                        </button>
                    </div>
                    <div id="custom-commands" style="margin-top: 5px;">
                        <!-- 自定义命令将在这里显示 -->
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }
    
    // 执行命令
    function executeCommand(command) {
        if (command && window.Evennia && window.Evennia.msg) {
            // 发送命令到服务器
            window.Evennia.msg("text", [command], {});
            
            // 在输入框中显示命令（可选）
            const inputField = $(".inputfield");
            if (inputField.length > 0) {
                inputField.val(command);
                inputField.focus();
                
                // 模拟回车键
                const event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13
                });
                inputField[0].dispatchEvent(event);
            }
        }
    }
    
    // 添加自定义命令
    function addCustomCommand() {
        const input = $("#custom-command");
        const command = input.val().trim();
        
        if (command) {
            // 添加到自定义命令列表
            customCommands.push({
                name: command.length > 8 ? command.substring(0, 8) + "..." : command,
                command: command,
                tooltip: "自定义命令: " + command
            });
            
            // 保存到localStorage
            localStorage.setItem('xianxia_custom_commands', JSON.stringify(customCommands));
            
            // 刷新显示
            updateCustomCommands();
            
            // 清空输入框
            input.val("");
        }
    }
    
    // 更新自定义命令显示
    function updateCustomCommands() {
        const container = $("#custom-commands");
        container.empty();
        
        customCommands.forEach((cmd, index) => {
            const button = $(`
                <div class="hotkey-button" 
                     data-command="${cmd.command}" 
                     title="${cmd.tooltip}"
                     style="margin: 2px; position: relative; display: inline-block;">
                    ${cmd.name}
                    <span style="position: absolute; top: -5px; right: -5px; 
                                 background: #FF4500; color: white; border-radius: 50%; 
                                 width: 16px; height: 16px; font-size: 10px; 
                                 display: flex; align-items: center; justify-content: center; 
                                 cursor: pointer;"
                          onclick="xianxia_hotbar_plugin.removeCustomCommand(${index}); event.stopPropagation();">
                        ×
                    </span>
                </div>
            `);
            
            button.on('click', function() {
                executeCommand(cmd.command);
            });
            
            container.append(button);
        });
    }
    
    // 删除自定义命令
    function removeCustomCommand(index) {
        customCommands.splice(index, 1);
        localStorage.setItem('xianxia_custom_commands', JSON.stringify(customCommands));
        updateCustomCommands();
    }
    
    // 加载自定义命令
    function loadCustomCommands() {
        const saved = localStorage.getItem('xianxia_custom_commands');
        if (saved) {
            try {
                customCommands = JSON.parse(saved);
            } catch (e) {
                console.log('加载自定义命令失败:', e);
                customCommands = [];
            }
        }
    }
    
    // 处理键盘快捷键
    var onKeydown = function(event) {
        // Ctrl + 数字键 执行对应的快捷命令
        if (event.ctrlKey && event.keyCode >= 49 && event.keyCode <= 57) {
            const index = event.keyCode - 49; // 1-9 对应 0-8
            if (index < defaultCommands.length) {
                executeCommand(defaultCommands[index].command);
                event.preventDefault();
                return false;
            }
        }
        
        return true;
    };
    
    // 注册组件到GoldenLayout
    var onLayoutChanged = function() {
        if (window.myLayout) {
            window.myLayout.registerComponent('xianxia_hotbar', function(container, componentState) {
                container.getElement().html(createHotbar());
                
                // 加载并显示自定义命令
                loadCustomCommands();
                updateCustomCommands();
                
                // 绑定回车键事件到自定义命令输入框
                container.getElement().find('#custom-command').on('keydown', function(e) {
                    if (e.keyCode === 13) { // Enter键
                        addCustomCommand();
                        e.preventDefault();
                    }
                });
            });
        }
    };
    
    // 初始化插件
    var init = function () {
        console.log('仙侠快捷命令栏插件已加载');
        
        // 加载自定义命令
        loadCustomCommands();
        
        // 如果GoldenLayout已经初始化，立即注册组件
        if (window.myLayout) {
            onLayoutChanged();
        }
    };
    
    // 公开的方法
    return {
        init: init,
        onKeydown: onKeydown,
        onLayoutChanged: onLayoutChanged,
        executeCommand: executeCommand,
        addCustomCommand: addCustomCommand,
        removeCustomCommand: removeCustomCommand
    };
})();

// 注册插件
window.plugin_handler.add('xianxia_hotbar', xianxia_hotbar_plugin);
