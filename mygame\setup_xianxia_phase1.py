"""
仙侠MUD第一阶段快速配置脚本

这个脚本帮助快速配置和测试仙侠MUD第一阶段功能。
在Evennia游戏中运行此脚本来自动创建测试环境。

使用方法：
@py exec(open('setup_xianxia_phase1.py').read())
"""

from evennia import create_object, search_object
from evennia.utils import logger
from typeclasses.xianxia import *

def create_demo_character():
    """创建演示角色"""
    print("创建演示角色...")
    
    # 创建角色
    char = create_object(
        XianxiaCharacter,
        key="演示修士",
        location=search_object("#2")[0]  # 默认房间
    )
    
    # 设置初始属性
    char.realm = Realm.QI_REFINING.value
    char.realm_level = 3
    char.cultivation_exp = 800
    char.spiritual_power = 200
    char.spiritual_power_max = 200
    
    # 学习基础技能
    char.learn_skill(XianxiaSkill.SWORD_MASTERY.value)
    char.learn_skill(XianxiaSkill.ALCHEMY.value)
    char.gain_skill_proficiency(XianxiaSkill.MEDITATION.value, 150)
    
    # 加入门派
    char.join_sect(Sect.SWORD_SECT.value)
    
    print(f"✓ 创建演示角色: {char.name}")
    print(f"  境界: {char.realm}{char.realm_level}层")
    print(f"  门派: {char.sect}")
    
    return char

def create_demo_treasures(location):
    """创建演示法宝"""
    print("创建演示法宝...")
    
    treasures = []
    
    # 创建各种武器
    weapons_data = [
        ("青锋剑", WeaponType.SWORD.value, TreasureGrade.SPIRITUAL.value),
        ("烈火刀", WeaponType.SABER.value, TreasureGrade.MORTAL.value),
        ("龙鳞枪", WeaponType.SPEAR.value, TreasureGrade.TREASURE.value),
        ("如意棍", WeaponType.STAFF.value, TreasureGrade.SPIRITUAL.value)
    ]
    
    for name, weapon_type, grade in weapons_data:
        weapon = create_object(
            XianxiaWeapon,
            key=name,
            location=location,
            attributes=[
                ("weapon_type", weapon_type),
                ("treasure_grade", grade),
                ("treasure_level", 1)
            ]
        )
        treasures.append(weapon)
        print(f"  ✓ {name} ({grade})")
    
    # 创建基础法宝
    treasure = create_object(
        XianxiaTreasure,
        key="聚灵珠",
        location=location,
        attributes=[
            ("treasure_grade", TreasureGrade.SPIRITUAL.value),
            ("treasure_level", 2)
        ]
    )
    treasures.append(treasure)
    print(f"  ✓ 聚灵珠 (灵品)")
    
    return treasures

def create_demo_pills(location):
    """创建演示丹药"""
    print("创建演示丹药...")
    
    pills = []
    
    # 创建各种丹药
    pills_data = [
        ("疗伤丹", PillType.HEALING.value, "中品"),
        ("回灵丹", PillType.SPIRITUAL.value, "下品"),
        ("破境丹", PillType.BREAKTHROUGH.value, "上品"),
        ("解毒丹", PillType.DETOX.value, "中品"),
        ("力量丹", PillType.STRENGTH.value, "下品")
    ]
    
    for name, pill_type, grade in pills_data:
        pill = create_object(
            XianxiaPill,
            key=name,
            location=location,
            attributes=[
                ("pill_type", pill_type),
                ("pill_grade", grade)
            ]
        )
        pills.append(pill)
        print(f"  ✓ {name} ({grade})")
    
    return pills

def create_demo_spirit_stones(location):
    """创建演示灵石"""
    print("创建演示灵石...")
    
    stones = []
    
    # 创建各品级灵石
    for grade in SpiritStoneGrade:
        stone = SpiritStone.create_spirit_stone(
            grade.value,
            location=location
        )
        stones.append(stone)
        print(f"  ✓ {grade.value}灵石")
    
    return stones

def create_demo_rooms():
    """创建演示房间"""
    print("创建演示房间...")
    
    rooms = []
    
    # 创建各种类型的房间
    room_data = [
        ("仙侠演示大厅", XianxiaRoom, "这是仙侠MUD的演示大厅，你可以在这里体验各种仙侠功能。"),
        ("修炼密室", CultivationRoom, "这是专门用于修炼的密室，灵气浓郁，是修炼的绝佳场所。"),
        ("炼丹房", AlchemyRoom, "这是炼丹房，丹炉火焰正旺，适合炼制各种丹药。"),
        ("宝库", TreasureVault, "这是宝库，存放着各种珍贵的法宝和材料。"),
        ("剑宗大殿", SectHall, "这是剑宗的大殿，威严庄重，供奉着历代宗主。"),
        ("野外竹林", Wilderness, "这是一片幽静的竹林，偶尔有灵兽出没。")
    ]
    
    for name, room_class, desc in room_data:
        room = create_object(
            room_class,
            key=name,
            location=None
        )
        room.db.desc = desc
        rooms.append(room)
        print(f"  ✓ {name}")
    
    # 连接房间
    main_hall = rooms[0]  # 演示大厅
    
    # 创建出口连接各个房间
    from evennia import create_object
    from evennia.objects.objects import DefaultExit
    
    directions = ["north", "south", "east", "west", "northeast", "northwest"]
    for i, room in enumerate(rooms[1:], 1):
        if i-1 < len(directions):
            direction = directions[i-1]
            # 创建去的出口
            exit_to = create_object(
                DefaultExit,
                key=direction,
                location=main_hall,
                destination=room
            )
            # 创建回来的出口
            back_direction = {
                "north": "south", "south": "north",
                "east": "west", "west": "east", 
                "northeast": "southwest", "northwest": "southeast"
            }.get(direction, "back")
            
            exit_back = create_object(
                DefaultExit,
                key=back_direction,
                location=room,
                destination=main_hall
            )
            
            print(f"    连接: {main_hall.name} <-> {room.name}")
    
    return rooms

def setup_demo_environment():
    """设置完整的演示环境"""
    print("=" * 50)
    print("🏮 仙侠MUD第一阶段演示环境设置")
    print("=" * 50)
    
    try:
        # 创建演示房间
        rooms = create_demo_rooms()
        main_hall = rooms[0]
        
        # 创建演示角色
        char = create_demo_character()
        char.move_to(main_hall, quiet=True)
        
        # 在宝库中创建道具
        treasure_vault = rooms[3]  # 宝库
        
        # 创建法宝
        treasures = create_demo_treasures(treasure_vault)
        
        # 创建丹药
        pills = create_demo_pills(treasure_vault)
        
        # 创建灵石
        stones = create_demo_spirit_stones(treasure_vault)
        
        # 给演示角色一些基础道具
        basic_sword = create_object(
            XianxiaWeapon,
            key="新手剑",
            location=char,
            attributes=[
                ("weapon_type", WeaponType.SWORD.value),
                ("treasure_grade", TreasureGrade.MORTAL.value)
            ]
        )
        
        basic_pill = create_object(
            XianxiaPill,
            key="基础疗伤丹",
            location=char,
            attributes=[
                ("pill_type", PillType.HEALING.value),
                ("pill_grade", "下品")
            ]
        )
        
        basic_stone = SpiritStone.create_spirit_stone(
            SpiritStoneGrade.LOW.value,
            location=char
        )
        
        print("\n" + "=" * 50)
        print("✅ 演示环境设置完成！")
        print("=" * 50)
        
        print(f"\n📍 主要位置:")
        print(f"  演示角色: {char.name} (在 {char.location.name})")
        print(f"  主要房间: {main_hall.name}")
        print(f"  宝库位置: {treasure_vault.name}")
        
        print(f"\n🎮 开始体验:")
        print(f"  1. 使用 'goto {char.name}' 传送到演示角色")
        print(f"  2. 使用 '状态' 查看角色信息")
        print(f"  3. 使用 '仙侠帮助' 查看所有命令")
        print(f"  4. 探索各个房间体验不同功能")
        
        print(f"\n🗺️  房间导航:")
        for i, room in enumerate(rooms):
            if i == 0:
                print(f"  中心: {room.name}")
            else:
                direction = ["north", "south", "east", "west", "northeast", "northwest"][i-1]
                print(f"  {direction}: {room.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

# 执行设置
if __name__ == "__main__":
    setup_demo_environment()
else:
    # 在Evennia中执行
    setup_demo_environment()
