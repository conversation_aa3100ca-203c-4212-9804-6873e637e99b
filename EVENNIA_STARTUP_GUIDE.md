# Evennia 项目成功启动详细记录

## 📋 项目概述
- **项目类型**: Evennia MUD/MUX/MUSH 开发框架
- **GitHub地址**: https://github.com/evennia/evennia
- **最终状态**: ✅ 成功启动并运行
- **启动时间**: 2025-06-27
- **操作系统**: Windows 11 (PowerShell)
- **Python版本**: 3.11.4

## 🚀 成功启动步骤

### 1. 环境检查
```powershell
# 检查Python版本
python --version
# 输出: Python 3.11.4
```

### 2. 安装Evennia
```powershell
# 通过pip安装Evennia (已预安装)
pip install evennia
# 输出: Requirement already satisfied: evennia (4.5.0)
```

### 3. 创建新游戏项目
```powershell
# 使用Python模块方式初始化项目
python -m evennia --init mygame
```
**成功输出**:
```
Welcome to Evennia!
Created a new Evennia game directory 'mygame'.
```

### 4. 数据库迁移
```powershell
cd mygame
python -m evennia migrate
```
**重要**: 必须使用 `python -m evennia` 而不是直接使用 `evennia` 命令，因为Windows上的启动器脚本有问题。

### 5. 创建超级用户
```powershell
python -m evennia start
```
**交互过程**:
- Username: `admin`
- Email: `<EMAIL>` (可选)
- Password: `AdminPass123!`
- 确认密码: `AdminPass123!`
- 绕过密码验证: `y`

## ❌ 遇到的问题及解决方案

### 问题1: Git克隆失败
**现象**: 
```
fatal: unable to access 'https://github.com/evennia/evennia.git/': Recv failure: Connection was reset
```
**解决方案**: 
直接使用pip安装而不是从源码安装：
```powershell
pip install evennia
```

### 问题2: Windows启动器脚本问题
**现象**: 
`evennia` 命令总是尝试打开文本编辑器显示启动器脚本内容
**解决方案**: 
使用Python模块方式调用：
```powershell
python -m evennia [命令]
```

### 问题3: 🔥 关键问题 - Twisted框架启动失败
**现象**: 
```
AttributeError: type object 'Callable' has no attribute '_abc_registry'
```
**根本原因**: 
系统中安装了过时的 `typing` 模块 (*******版本)，与Python 3.11不兼容

**解决方案**: 
```powershell
pip uninstall typing -y
```
**说明**: Python 3.11内置了typing模块，外部安装的旧版本会造成冲突

### 问题4: Portal连接超时
**现象**: 
```
Connection to Evennia timed out. Try again.
```
**解决方案**: 
卸载冲突的typing模块后问题自动解决

## ✅ 最终成功启动

### 启动命令
```powershell
cd mygame
python -m evennia start
```

### 成功输出
```
Portal starting ...
... Portal started. 
Server starting  ...
... Server started.
Evennia running.   
---------------------- Evennia ---
mygame Portal 4.5.0
    external ports:
        telnet: 4000
        webserver-proxy: 4001
        webclient-websocket: 4002
    internal_ports (to Server):
        webserver: 4005
        amp: 4006

mygame Server 4.5.0
    internal ports (to Portal):
        webserver: 4005
        amp : 4006
----------------------------------
```

### 验证运行状态
```powershell
python -m evennia status
```
**输出**:
```
Portal: RUNNING (pid 17596)
Server: RUNNING (pid 6392)
```

## 🌐 访问信息

### 服务端口
- **Telnet**: `localhost:4000` (MUD客户端连接)
- **Web界面**: `http://localhost:4001` (浏览器访问)
- **WebSocket**: `localhost:4002` (Web客户端)

### 管理员账户
- **用户名**: `admin`
- **密码**: `AdminPass123!`

## 🔧 重要技术细节

### 为什么使用 `python -m evennia`
1. Windows上的 `evennia.bat` 脚本有路径问题
2. 直接调用Python模块更可靠
3. 避免了启动器脚本的兼容性问题

### typing模块冲突解释
1. Python 3.5+内置了typing模块
2. 旧版本的外部typing包会覆盖内置模块
3. 导致Twisted框架无法正确导入类型定义
4. 卸载外部typing包让Python使用内置版本

## 📝 最佳实践建议

### 1. 环境准备
- 使用Python 3.8+版本
- 确保没有冲突的typing模块
- 在Windows上优先使用 `python -m evennia`

### 2. 常用命令
```powershell
# 启动服务器
python -m evennia start

# 查看状态
python -m evennia status

# 停止服务器
python -m evennia stop

# 重启服务器
python -m evennia restart

# 查看日志
python -m evennia start -l
```

### 3. 故障排除
- 如果启动失败，检查是否有typing模块冲突
- 确保在正确的游戏目录中运行命令
- 使用 `-l` 参数查看详细日志

## 🎯 项目结构
```
mygame/
├── commands/          # 游戏命令
├── server/           # 服务器配置
│   ├── conf/        # 设置文件
│   └── logs/        # 日志文件
├── typeclasses/     # 游戏对象类型
├── web/             # Web界面
└── world/           # 游戏世界数据
```

## 🚀 下一步开发
1. 通过Web界面熟悉Evennia
2. 阅读官方文档学习游戏开发
3. 自定义游戏世界和规则
4. 添加自定义命令和功能

## 🔍 详细故障排除过程

### Context7文档查询
使用Context7 MCP获取了最新的Evennia官方文档，确认了正确的安装和启动流程：
- 标准流程: `evennia --init mygame` → `cd mygame` → `evennia migrate` → `evennia start`
- Windows特殊处理: 需要使用 `python -m evennia` 替代直接的 `evennia` 命令

### 错误诊断时间线

#### 第一次尝试 (失败)
```powershell
git clone https://github.com/evennia/evennia.git .
# 错误: 网络连接问题
```

#### 第二次尝试 (部分成功)
```powershell
pip install evennia  # ✅ 成功
evennia --init mygame  # ❌ 启动器脚本问题
```

#### 第三次尝试 (数据库问题)
```powershell
python -m evennia --init mygame  # ✅ 成功
cd mygame
python -m evennia migrate  # ✅ 成功
python -m evennia start  # ❌ Twisted框架错误
```

#### 最终成功
```powershell
pip uninstall typing -y  # 🔑 关键步骤
python -m evennia start  # ✅ 完全成功
```

## 🧪 测试验证

### 功能测试
1. **服务器状态检查**:
   ```powershell
   python -m evennia status
   # 输出: Portal: RUNNING, Server: RUNNING
   ```

2. **Web服务测试**:
   ```powershell
   curl http://localhost:4001
   # 返回: HTML响应 (3792字节)
   ```

3. **端口监听验证**:
   ```powershell
   netstat -an | findstr ":4000"
   # 确认端口4000-4002正在监听
   ```

## 🐛 常见错误及解决方案

### 错误1: "no such table: accounts_accountdb"
**原因**: 数据库未初始化
**解决**: `python -m evennia migrate`

### 错误2: "evennia命令打开文本编辑器"
**原因**: Windows启动器脚本路径问题
**解决**: 使用 `python -m evennia` 替代

### 错误3: "AttributeError: type object 'Callable'"
**原因**: typing模块版本冲突
**解决**: `pip uninstall typing -y`

### 错误4: "Connection to Evennia timed out"
**原因**: 通常是typing模块冲突的连锁反应
**解决**: 解决typing冲突后自动修复

## 📊 性能信息

### 启动时间
- 数据库迁移: ~30秒
- 服务器启动: ~10秒
- 总启动时间: ~40秒

### 内存使用
- Portal进程: PID 17596
- Server进程: PID 6392
- 总体轻量级，适合开发环境

## 🔐 安全注意事项

### 开发环境设置
```python
# settings.py中的开发配置
IN_GAME_ERRORS = True  # 显示详细错误信息
ALLOWED_HOSTS = ['*']  # 允许所有主机访问
SERVER_HOSTNAME = 'localhost'  # 本地开发
```

### 生产环境建议
- 修改 `ALLOWED_HOSTS` 为具体域名
- 设置 `IN_GAME_ERRORS = False`
- 使用强密码策略
- 配置防火墙规则

## 📚 参考资源

### 官方文档
- 主站: https://www.evennia.com
- 文档: https://www.evennia.com/docs/latest/
- GitHub: https://github.com/evennia/evennia

### 社区支持
- Discord: https://discord.gg/AJJpcRUhtF
- 论坛: http://www.evennia.com/discussions

### 开发工具
- PyCharm配置支持
- IPython shell增强: `pip install ipython`
- 代码格式化: 内置black支持

---
**记录时间**: 2025-06-27
**记录者**: Augment Agent
**状态**: ✅ 项目成功启动并运行
**文档版本**: 1.0 (详细版)
