/*
 * 仙侠MUD专用GoldenLayout配置
 * 定义了适合仙侠游戏的界面布局
 */

var goldenlayout_config = {
    settings: {
        hasHeaders: true,
        constrainDragToContainer: true,
        reorderEnabled: true,
        selectionEnabled: false,
        popoutWholeStack: false,
        blockedPopoutsThrowError: true,
        closePopoutsOnUnload: true,
        showPopoutIcon: false,
        showMaximiseIcon: true,
        showCloseIcon: true,
        responsiveMode: 'onload',
        tabOverlapAllowance: 0,
        reorderOnTabMenuClick: true,
        tabControlOffset: 10
    },
    
    dimensions: {
        borderWidth: 5,
        minItemHeight: 200,
        minItemWidth: 250,
        headerHeight: 30,
        dragProxyWidth: 300,
        dragProxyHeight: 200
    },
    
    labels: {
        close: '关闭',
        maximise: '最大化',
        minimise: '最小化',
        popout: '弹出',
        popin: '嵌入',
        tabDropdown: '选择标签页'
    },
    
    content: [{
        type: "row",
        content: [{
            // 左侧主要游戏区域
            type: "column",
            width: 70,
            content: [{
                // 主游戏窗口
                type: "component",
                componentName: "Main",
                title: "仙侠世界",
                height: 80,
                isClosable: false,
                reorderEnabled: false
            }, {
                // 输入区域
                type: "component", 
                componentName: "input",
                title: "命令输入",
                height: 20,
                isClosable: false,
                reorderEnabled: false
            }]
        }, {
            // 右侧信息面板区域
            type: "column",
            width: 30,
            content: [{
                // 角色状态面板
                type: "component",
                componentName: "xianxia_stats",
                title: "修仙状态",
                height: 40,
                isClosable: false
            }, {
                // 快捷命令栏
                type: "component",
                componentName: "xianxia_hotbar", 
                title: "快捷命令",
                height: 35,
                isClosable: false
            }, {
                // 聊天频道（可选）
                type: "stack",
                height: 25,
                content: [{
                    type: "component",
                    componentName: "channels",
                    title: "聊天频道"
                }, {
                    type: "component", 
                    componentName: "help",
                    title: "帮助信息"
                }]
            }]
        }]
    }]
};

// 备用布局配置 - 移动端友好
var goldenlayout_mobile_config = {
    settings: {
        hasHeaders: true,
        constrainDragToContainer: true,
        reorderEnabled: true,
        selectionEnabled: false,
        popoutWholeStack: false,
        blockedPopoutsThrowError: true,
        closePopoutsOnUnload: true,
        showPopoutIcon: false,
        showMaximiseIcon: true,
        showCloseIcon: true,
        responsiveMode: 'onload'
    },
    
    content: [{
        type: "stack",
        content: [{
            // 主游戏窗口
            type: "component",
            componentName: "Main",
            title: "仙侠世界",
            isClosable: false
        }, {
            // 角色状态
            type: "component",
            componentName: "xianxia_stats", 
            title: "修仙状态",
            isClosable: false
        }, {
            // 快捷命令
            type: "component",
            componentName: "xianxia_hotbar",
            title: "快捷命令", 
            isClosable: false
        }, {
            // 输入区域
            type: "component",
            componentName: "input",
            title: "命令输入",
            isClosable: false
        }]
    }]
};

// 简化布局配置 - 适合小屏幕
var goldenlayout_simple_config = {
    settings: {
        hasHeaders: true,
        showPopoutIcon: false,
        showMaximiseIcon: false,
        showCloseIcon: false
    },
    
    content: [{
        type: "column",
        content: [{
            type: "row",
            height: 85,
            content: [{
                // 主游戏区域
                type: "component",
                componentName: "Main", 
                title: "游戏",
                width: 75,
                isClosable: false
            }, {
                // 状态面板
                type: "component",
                componentName: "xianxia_stats",
                title: "状态",
                width: 25,
                isClosable: false
            }]
        }, {
            type: "row", 
            height: 15,
            content: [{
                // 输入区域
                type: "component",
                componentName: "input",
                title: "输入",
                width: 60,
                isClosable: false
            }, {
                // 快捷命令
                type: "component", 
                componentName: "xianxia_hotbar",
                title: "命令",
                width: 40,
                isClosable: false
            }]
        }]
    }]
};

// 根据屏幕大小选择合适的布局
function getOptimalLayout() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    if (width < 768) {
        // 移动设备
        return goldenlayout_mobile_config;
    } else if (width < 1024) {
        // 平板设备
        return goldenlayout_simple_config;
    } else {
        // 桌面设备
        return goldenlayout_config;
    }
}

// 动态设置布局配置
if (typeof window !== 'undefined') {
    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        // 可以在这里实现响应式布局切换
        console.log('窗口大小变化，当前宽度:', window.innerWidth);
    });
    
    // 设置初始布局
    goldenlayout_config = getOptimalLayout();
}

// 导出配置供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        default: goldenlayout_config,
        mobile: goldenlayout_mobile_config,
        simple: goldenlayout_simple_config,
        getOptimalLayout: getOptimalLayout
    };
}
