# 🏮 仙侠MUD网页测试指南

## ✅ 系统状态

**服务器状态**: ✅ 正在运行
- Portal: RUNNING (pid 17596)
- Server: RUNNING (pid 6392)

**已完成的配置**:
- ✅ 仙侠角色系统已配置
- ✅ 仙侠房间系统已配置  
- ✅ 仙侠命令集已配置
- ✅ 测试角色"测试修士"已创建
- ✅ 测试房间"仙侠演示大厅"已创建

## 🌐 网页访问

### 访问地址
打开浏览器访问：**http://localhost:4001**

### 登录信息
- **用户名**: 创建新账户或使用现有账户
- **密码**: 设置您的密码

## 🎮 测试步骤

### 第一步：创建账户和角色
1. 在网页上注册新账户
2. 创建角色时会自动使用仙侠角色系统
3. 角色将自动成为"练气期1层"的修士

### 第二步：基础功能测试

#### 🧙‍♂️ 角色系统测试
```
状态          # 查看角色详细状态
技能          # 查看技能列表
仙侠帮助      # 查看所有仙侠命令
```

**预期结果**:
- 显示境界、灵力、门派等信息
- 显示已学会的基础技能
- 显示完整的仙侠命令列表

#### 🧘‍♂️ 修炼系统测试
```
修炼          # 开始修炼(默认60分钟)
修炼 30       # 修炼30分钟
停止修炼      # 停止当前修炼
突破          # 尝试突破境界
```

**预期结果**:
- 修炼会增加修炼经验
- 经验满足后可以突破
- 突破成功会提升境界层次

#### 🏠 房间系统测试
```
look          # 查看房间详情
```

**预期结果**:
- 显示房间的灵气浓度和风水类型
- 显示修炼效果加成信息

#### 🏛️ 门派系统测试
```
加入门派 剑宗    # 加入剑宗
离开门派        # 离开当前门派
```

**预期结果**:
- 成功加入门派
- 状态中显示门派信息

### 第三步：高级功能测试

#### 🗡️ 法宝系统测试
如果有管理员权限，可以创建法宝进行测试：
```
@create typeclasses.xianxia.objects.XianxiaWeapon:测试剑
认主 测试剑
提升认主 测试剑
法宝
```

#### 💊 丹药系统测试
```
@create typeclasses.xianxia.objects.XianxiaPill:疗伤丹
服用 疗伤丹
```

#### 💎 灵石系统测试
```
@create typeclasses.xianxia.objects.SpiritStone:下品灵石
吸收 下品灵石
```

## 🔍 测试重点

### 必须验证的功能
1. **角色创建** - 是否自动成为仙侠角色
2. **状态显示** - 境界、灵力等信息是否正确
3. **修炼系统** - 修炼和突破是否正常工作
4. **命令系统** - 仙侠命令是否可用
5. **房间效果** - 房间信息是否正确显示

### 可能遇到的问题
1. **命令不识别** - 检查命令集是否正确加载
2. **角色类型错误** - 检查是否使用了仙侠角色类
3. **中文显示问题** - 检查编码设置
4. **功能异常** - 查看服务器日志

## 🐛 问题排查

### 如果命令不工作
1. 检查角色是否为仙侠角色类型
2. 重新登录游戏
3. 检查服务器日志

### 如果显示异常
1. 刷新网页
2. 检查浏览器控制台错误
3. 尝试不同浏览器

### 查看服务器日志
在服务器终端查看实时日志：
```
python -m evennia -l
```

## 📊 测试报告模板

请按以下格式提供测试反馈：

### ✅ 成功的功能
- [ ] 角色创建和仙侠属性显示
- [ ] 修炼系统工作正常
- [ ] 突破系统工作正常
- [ ] 门派系统工作正常
- [ ] 房间效果显示正常
- [ ] 仙侠命令可用

### ❌ 发现的问题
- 问题描述：
- 重现步骤：
- 预期结果：
- 实际结果：

### 💡 改进建议
- 用户体验方面：
- 功能完善方面：
- 界面优化方面：

## 🚀 下一步计划

测试完成后，我们将进入第二阶段开发：
1. **随机敌人系统** - 动态生成和刷新
2. **任务系统** - 各种类型的仙侠任务
3. **战斗系统优化** - 仙侠特色战斗
4. **副本系统** - 随机生成的仙侠副本

---

**测试负责人**: 用户手动测试  
**技术支持**: Augment Agent  
**测试日期**: 2025-06-27
