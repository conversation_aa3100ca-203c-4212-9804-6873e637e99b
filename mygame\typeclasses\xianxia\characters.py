"""
仙侠角色系统
基于Evennia官方Character系统扩展的仙侠角色类

功能包括：
- 境界系统 (练气期、筑基期、金丹期等)
- 灵力属性和恢复机制
- 门派系统和门派技能
- 修炼系统和境界突破
- 仙侠技能系统
"""

from evennia import DefaultCharacter, AttributeProperty
from evennia.utils.utils import lazy_property
from enum import Enum
import random
from datetime import datetime, timedelta


# =============================================================================
# 仙侠枚举定义
# =============================================================================

class Realm(Enum):
    """修炼境界"""
    MORTAL = "凡人"
    QI_REFINING = "练气期"
    FOUNDATION = "筑基期"
    GOLDEN_CORE = "金丹期"
    NASCENT_SOUL = "元婴期"
    SOUL_FORMATION = "化神期"
    VOID_REFINEMENT = "炼虚期"
    BODY_INTEGRATION = "合体期"
    MAHAYANA = "大乘期"
    TRIBULATION = "渡劫期"
    IMMORTAL = "仙人"


class Sect(Enum):
    """门派"""
    NONE = "散修"
    SWORD_SECT = "剑宗"
    PILL_SECT = "丹宗"
    FORMATION_SECT = "阵法门"
    BEAST_SECT = "御兽门"
    DEMON_SECT = "魔道宗"
    BUDDHIST_SECT = "佛门"
    TALISMAN_SECT = "符箓派"


class XianxiaSkill(Enum):
    """仙侠技能"""
    SWORD_MASTERY = "剑法"
    INTERNAL_POWER = "内功"
    ALCHEMY = "炼丹"
    FORMATION = "阵法"
    TALISMAN = "符箓"
    BEAST_TAMING = "御兽"
    MEDITATION = "打坐"
    BODY_REFINING = "炼体"


# =============================================================================
# 仙侠角色基类
# =============================================================================

class XianxiaCharacter(DefaultCharacter):
    """
    仙侠角色基类
    扩展了Evennia的DefaultCharacter，添加仙侠特色功能
    """
    
    # 基础属性标记
    is_pc = True
    
    # =============================================================================
    # 境界和修炼属性
    # =============================================================================
    
    realm = AttributeProperty(Realm.MORTAL.value, autocreate=True)
    realm_level = AttributeProperty(1, autocreate=True)  # 境界内的等级 (1-9)
    
    # 灵力系统
    spiritual_power = AttributeProperty(100, autocreate=True)
    spiritual_power_max = AttributeProperty(100, autocreate=True)
    spiritual_power_regen = AttributeProperty(1, autocreate=True)  # 每分钟恢复
    
    # 修炼经验
    cultivation_exp = AttributeProperty(0, autocreate=True)
    cultivation_exp_needed = AttributeProperty(1000, autocreate=True)
    
    # =============================================================================
    # 门派和社交属性
    # =============================================================================
    
    sect = AttributeProperty(Sect.NONE.value, autocreate=True)
    sect_rank = AttributeProperty("外门弟子", autocreate=True)
    sect_contribution = AttributeProperty(0, autocreate=True)
    
    # 师父和师兄弟关系
    master_id = AttributeProperty(None, autocreate=True)
    disciples = AttributeProperty([], autocreate=True)
    
    # =============================================================================
    # 技能系统
    # =============================================================================
    
    # 技能等级 (字典格式: {技能名: 等级})
    skills = AttributeProperty({}, autocreate=True)
    
    # 技能熟练度 (字典格式: {技能名: 熟练度})
    skill_proficiency = AttributeProperty({}, autocreate=True)
    
    # =============================================================================
    # 特殊状态
    # =============================================================================
    
    # 修炼状态
    is_cultivating = AttributeProperty(False, autocreate=True)
    cultivation_start_time = AttributeProperty(None, autocreate=True)
    
    # 突破状态
    is_breaking_through = AttributeProperty(False, autocreate=True)
    breakthrough_progress = AttributeProperty(0, autocreate=True)
    
    # 走火入魔风险
    qi_deviation_risk = AttributeProperty(0, autocreate=True)  # 0-100
    
    def at_object_creation(self):
        """角色创建时的初始化"""
        super().at_object_creation()
        
        # 初始化基础属性
        self.realm = Realm.QI_REFINING.value
        self.realm_level = 1
        
        # 初始化灵力
        self.spiritual_power = 100
        self.spiritual_power_max = 100
        
        # 初始化技能
        self.skills = {
            XianxiaSkill.MEDITATION.value: 1,
            XianxiaSkill.INTERNAL_POWER.value: 1
        }
        self.skill_proficiency = {
            XianxiaSkill.MEDITATION.value: 0,
            XianxiaSkill.INTERNAL_POWER.value: 0
        }
        
        # 添加角色标签
        self.tags.add("xianxia_character", category="character_type")
        
        # 启动灵力恢复
        self.start_spiritual_power_regen()
        
    # =============================================================================
    # 境界和修炼系统
    # =============================================================================
    
    def get_realm_info(self):
        """获取境界信息"""
        return {
            "realm": self.realm,
            "level": self.realm_level,
            "display": f"{self.realm}{self.realm_level}层",
            "spiritual_power": f"{self.spiritual_power}/{self.spiritual_power_max}",
            "cultivation_progress": f"{self.cultivation_exp}/{self.cultivation_exp_needed}"
        }
        
    def can_breakthrough(self):
        """检查是否可以突破"""
        # 需要满足经验要求
        if self.cultivation_exp < self.cultivation_exp_needed:
            return False, "修炼经验不足"
            
        # 不能在修炼中突破
        if self.is_cultivating:
            return False, "修炼中无法突破"
            
        # 不能在已经突破中
        if self.is_breaking_through:
            return False, "正在突破中"
            
        # 检查境界上限
        if self.realm_level >= 9 and self.realm != Realm.TRIBULATION.value:
            return False, "需要先突破大境界"
            
        return True, "可以突破"
        
    def start_breakthrough(self):
        """开始突破"""
        can_break, reason = self.can_breakthrough()
        if not can_break:
            self.msg(f"无法突破：{reason}")
            return False
            
        self.is_breaking_through = True
        self.breakthrough_progress = 0
        
        # 突破有一定风险
        risk = self.calculate_breakthrough_risk()
        self.qi_deviation_risk = risk
        
        self.msg(f"开始突破{self.realm}{self.realm_level + 1}层...")
        self.msg(f"突破风险：{risk}%")
        
        # 启动突破过程 (这里简化为立即完成，实际可以做成定时器)
        return self.complete_breakthrough()
        
    def complete_breakthrough(self):
        """完成突破"""
        # 检查是否成功
        success_chance = 100 - self.qi_deviation_risk
        if random.randint(1, 100) <= success_chance:
            # 突破成功
            self.realm_level += 1
            self.cultivation_exp = 0
            self.cultivation_exp_needed = self.calculate_next_exp_needed()
            
            # 提升灵力上限
            self.spiritual_power_max += 50
            self.spiritual_power = self.spiritual_power_max
            
            self.msg(f"突破成功！现在是{self.realm}{self.realm_level}层")
            
            # 检查是否需要大境界突破
            if self.realm_level >= 9:
                self.msg("已达到当前境界巅峰，可尝试突破大境界！")
                
        else:
            # 突破失败
            self.msg("突破失败！走火入魔！")
            self.handle_qi_deviation()
            
        self.is_breaking_through = False
        self.breakthrough_progress = 0
        self.qi_deviation_risk = 0
        
        return True
        
    def calculate_breakthrough_risk(self):
        """计算突破风险"""
        base_risk = 20  # 基础风险20%
        
        # 境界越高风险越大
        realm_risk = self.realm_level * 5
        
        # 技能等级可以降低风险
        meditation_level = self.skills.get(XianxiaSkill.MEDITATION.value, 1)
        internal_power_level = self.skills.get(XianxiaSkill.INTERNAL_POWER.value, 1)
        skill_reduction = (meditation_level + internal_power_level) * 2
        
        total_risk = max(5, base_risk + realm_risk - skill_reduction)
        return min(95, total_risk)
        
    def calculate_next_exp_needed(self):
        """计算下一级所需经验"""
        base_exp = 1000
        level_multiplier = self.realm_level * 1.5
        return int(base_exp * level_multiplier)
        
    def handle_qi_deviation(self):
        """处理走火入魔"""
        # 损失部分修为
        exp_loss = self.cultivation_exp // 2
        self.cultivation_exp = max(0, self.cultivation_exp - exp_loss)
        
        # 损失灵力
        power_loss = self.spiritual_power_max // 4
        self.spiritual_power_max = max(50, self.spiritual_power_max - power_loss)
        self.spiritual_power = min(self.spiritual_power, self.spiritual_power_max)
        
        self.msg(f"走火入魔！损失{exp_loss}修炼经验，灵力上限降低{power_loss}")
        
    # =============================================================================
    # 修炼系统
    # =============================================================================
    
    def start_cultivation(self, duration_minutes=60):
        """开始修炼"""
        if self.is_cultivating:
            self.msg("你已经在修炼中了。")
            return False
            
        if self.is_breaking_through:
            self.msg("突破中无法修炼。")
            return False
            
        self.is_cultivating = True
        self.cultivation_start_time = datetime.now()
        
        self.msg(f"开始修炼，预计{duration_minutes}分钟...")
        
        # 这里可以设置定时器，简化版本直接计算结果
        self.complete_cultivation(duration_minutes)
        return True
        
    def complete_cultivation(self, duration_minutes):
        """完成修炼"""
        if not self.is_cultivating:
            return
            
        # 计算修炼收益
        base_exp = 10 * duration_minutes
        
        # 技能加成
        meditation_level = self.skills.get(XianxiaSkill.MEDITATION.value, 1)
        skill_bonus = 1 + (meditation_level * 0.1)
        
        # 境界影响 (高境界修炼更难)
        realm_penalty = 1 - (self.realm_level * 0.05)
        realm_penalty = max(0.5, realm_penalty)
        
        final_exp = int(base_exp * skill_bonus * realm_penalty)
        
        self.cultivation_exp += final_exp
        self.msg(f"修炼完成，获得{final_exp}点修炼经验")
        
        # 提升打坐技能熟练度
        self.gain_skill_proficiency(XianxiaSkill.MEDITATION.value, duration_minutes)
        
        # 检查是否可以突破
        if self.cultivation_exp >= self.cultivation_exp_needed:
            self.msg("修炼经验已满，可以尝试突破了！")
            
        self.is_cultivating = False
        self.cultivation_start_time = None
        
    def stop_cultivation(self):
        """停止修炼"""
        if not self.is_cultivating:
            self.msg("你没有在修炼。")
            return False
            
        # 计算已修炼时间
        if self.cultivation_start_time:
            elapsed = datetime.now() - self.cultivation_start_time
            minutes = elapsed.total_seconds() / 60
            self.complete_cultivation(int(minutes))
        else:
            self.is_cultivating = False
            self.msg("停止修炼。")
            
        return True
        
    # =============================================================================
    # 技能系统
    # =============================================================================
    
    def get_skill_level(self, skill_name):
        """获取技能等级"""
        return self.skills.get(skill_name, 0)
        
    def get_skill_proficiency(self, skill_name):
        """获取技能熟练度"""
        return self.skill_proficiency.get(skill_name, 0)
        
    def gain_skill_proficiency(self, skill_name, amount):
        """增加技能熟练度"""
        if skill_name not in self.skill_proficiency:
            self.skill_proficiency[skill_name] = 0
            
        self.skill_proficiency[skill_name] += amount
        
        # 检查是否可以升级
        current_level = self.skills.get(skill_name, 0)
        needed_proficiency = self.calculate_skill_proficiency_needed(current_level)
        
        if self.skill_proficiency[skill_name] >= needed_proficiency:
            self.level_up_skill(skill_name)
            
    def level_up_skill(self, skill_name):
        """技能升级"""
        current_level = self.skills.get(skill_name, 0)
        new_level = current_level + 1
        
        self.skills[skill_name] = new_level
        self.skill_proficiency[skill_name] = 0  # 重置熟练度
        
        self.msg(f"{skill_name}技能提升到{new_level}级！")
        
    def calculate_skill_proficiency_needed(self, level):
        """计算技能升级所需熟练度"""
        return 100 * (level + 1)
        
    def learn_skill(self, skill_name, teacher=None):
        """学习新技能"""
        if skill_name in self.skills:
            self.msg(f"你已经学会了{skill_name}。")
            return False
            
        # 检查是否有师父或教师
        if teacher:
            teacher_skill_level = teacher.get_skill_level(skill_name)
            if teacher_skill_level < 3:
                self.msg(f"{teacher.name}的{skill_name}技能不足以教导你。")
                return False
                
        self.skills[skill_name] = 1
        self.skill_proficiency[skill_name] = 0
        
        self.msg(f"学会了{skill_name}技能！")
        return True
        
    # =============================================================================
    # 门派系统
    # =============================================================================
    
    def join_sect(self, sect_name, rank="外门弟子"):
        """加入门派"""
        if self.sect != Sect.NONE.value:
            self.msg(f"你已经是{self.sect}的弟子了。")
            return False
            
        self.sect = sect_name
        self.sect_rank = rank
        self.sect_contribution = 0
        
        self.msg(f"成功加入{sect_name}，成为{rank}！")
        return True
        
    def leave_sect(self):
        """离开门派"""
        if self.sect == Sect.NONE.value:
            self.msg("你本就是散修。")
            return False
            
        old_sect = self.sect
        self.sect = Sect.NONE.value
        self.sect_rank = "散修"
        self.sect_contribution = 0
        
        self.msg(f"离开了{old_sect}，重新成为散修。")
        return True
        
    def gain_sect_contribution(self, amount):
        """获得门派贡献"""
        if self.sect == Sect.NONE.value:
            return False
            
        self.sect_contribution += amount
        self.msg(f"获得{amount}点门派贡献，总计{self.sect_contribution}点")
        return True
        
    # =============================================================================
    # 灵力系统
    # =============================================================================
    
    def start_spiritual_power_regen(self):
        """启动灵力恢复 (这里简化处理，实际应该用定时器)"""
        # 在实际实现中，这里应该启动一个定时器
        pass
        
    def consume_spiritual_power(self, amount):
        """消耗灵力"""
        if self.spiritual_power < amount:
            return False
            
        self.spiritual_power -= amount
        return True
        
    def restore_spiritual_power(self, amount):
        """恢复灵力"""
        self.spiritual_power = min(
            self.spiritual_power + amount,
            self.spiritual_power_max
        )
        
    # =============================================================================
    # 显示和状态方法
    # =============================================================================
    
    def get_status_display(self):
        """获取状态显示"""
        status = []
        status.append(f"境界：{self.realm}{self.realm_level}层")
        status.append(f"门派：{self.sect} ({self.sect_rank})")
        status.append(f"灵力：{self.spiritual_power}/{self.spiritual_power_max}")
        status.append(f"修炼进度：{self.cultivation_exp}/{self.cultivation_exp_needed}")
        
        if self.is_cultivating:
            status.append("状态：修炼中")
        elif self.is_breaking_through:
            status.append("状态：突破中")
        else:
            status.append("状态：正常")
            
        return "\n".join(status)
        
    def get_skills_display(self):
        """获取技能显示"""
        if not self.skills:
            return "尚未学会任何技能"
            
        skill_lines = []
        for skill_name, level in self.skills.items():
            proficiency = self.skill_proficiency.get(skill_name, 0)
            needed = self.calculate_skill_proficiency_needed(level)
            skill_lines.append(f"{skill_name}: {level}级 ({proficiency}/{needed})")
            
        return "\n".join(skill_lines)
        
    def return_appearance(self, looker, **kwargs):
        """重写外观显示，添加仙侠信息"""
        text = super().return_appearance(looker, **kwargs)
        
        # 添加境界信息
        realm_info = f"\n境界：{self.realm}{self.realm_level}层"
        if self.sect != Sect.NONE.value:
            realm_info += f" | 门派：{self.sect}"
            
        # 在描述后添加境界信息
        if "\n" in text:
            lines = text.split("\n")
            lines.insert(1, realm_info)
            text = "\n".join(lines)
        else:
            text += realm_info
            
        return text
