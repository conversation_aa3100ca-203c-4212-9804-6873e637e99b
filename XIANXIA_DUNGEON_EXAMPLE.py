"""
仙侠副本系统实现示例
基于Evennia官方的DungeonBranch系统扩展

使用方法:
1. 将此代码集成到游戏中
2. 创建仙侠副本入口房间
3. 玩家进入后自动生成随机副本
"""

from evennia import DefaultScript, DefaultRoom, DefaultExit, create
from evennia.utils import search
from evennia.contrib.tutorials.evadventure.dungeon import (
    EvAdventureDungeonBranch, EvAdventureDungeonRoom, 
    EvAdventureDungeonExit, EvAdventureDungeonStartRoom
)
import random
from datetime import datetime, timedelta
from math import sqrt


# =============================================================================
# 仙侠副本房间系统
# =============================================================================

class XianxiaDungeonRoom(EvAdventureDungeonRoom):
    """
    仙侠副本房间
    扩展了基础副本房间，添加仙侠特色功能
    """
    
    # 仙侠特色属性
    spiritual_density = AttributeProperty(1.0, autocreate=True)  # 灵气浓度
    feng_shui_type = AttributeProperty("普通", autocreate=True)   # 风水类型
    area_type = AttributeProperty("外围", autocreate=True)        # 区域类型
    hidden_treasures = AttributeProperty([], autocreate=True)    # 隐藏宝物
    special_effects = AttributeProperty([], autocreate=True)     # 特殊效果
    
    def at_object_creation(self):
        """房间创建时的初始化"""
        super().at_object_creation()
        
        # 根据深度设置灵气浓度
        depth = self.attributes.get("depth", 1)
        self.spiritual_density = 1.0 + (depth * 0.2)
        
        # 随机设置风水类型
        feng_shui_types = ["普通", "聚灵", "煞气", "祥瑞", "凶险"]
        weights = [50, 20, 15, 10, 5]  # 权重
        self.feng_shui_type = random.choices(feng_shui_types, weights=weights)[0]
        
    def get_display_header(self, looker, **kwargs):
        """显示房间头部信息"""
        header = super().get_display_header(looker, **kwargs)
        
        # 添加仙侠风格信息
        xianxia_info = f"\n|c[{self.area_type}] 灵气浓度: {self.spiritual_density:.1f} 风水: {self.feng_shui_type}|n"
        
        return header + xianxia_info
        
    def apply_room_effects(self, character):
        """应用房间效果到角色"""
        if self.feng_shui_type == "聚灵":
            # 灵力恢复加速
            character.spiritual_power_regen *= 1.5
            character.msg("|g这里的灵气格外浓郁，你感到灵力恢复加快了。|n")
            
        elif self.feng_shui_type == "煞气":
            # 灵力消耗增加
            character.msg("|r这里煞气逼人，你感到灵力消耗加剧。|n")
            
        elif self.feng_shui_type == "祥瑞":
            # 随机获得临时加成
            character.msg("|y祥瑞之气环绕，你感到实力有所提升。|n")


# =============================================================================
# 仙侠副本分支管理器
# =============================================================================

class XianxiaDungeonBranch(EvAdventureDungeonBranch):
    """
    仙侠副本分支管理器
    管理单个仙侠副本实例
    """
    
    # 仙侠副本特色属性
    realm_type = AttributeProperty("剑冢", autocreate=True)      # 秘境类型
    difficulty_level = AttributeProperty(1, autocreate=True)    # 难度等级
    boss_defeated = AttributeProperty(False, autocreate=True)   # BOSS是否被击败
    special_events = AttributeProperty([], autocreate=True)     # 特殊事件记录
    
    # 副本配置
    max_unexplored_exits = 3  # 增加探索性
    max_new_exits_per_room = 3
    
    def at_script_creation(self):
        """脚本创建时初始化"""
        super().at_script_creation()
        self.key = f"xianxia_dungeon_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def new_room(self, from_exit):
        """创建新的仙侠副本房间"""
        self.last_updated = datetime.utcnow()
        
        # 获取源房间坐标
        source_location = from_exit.location
        x, y = source_location.attributes.get("xy_coords", default=(0, 0))
        
        # 计算新房间坐标
        direction_shifts = {
            "north": (0, 1), "south": (0, -1),
            "east": (1, 0), "west": (-1, 0),
            "northeast": (1, 1), "northwest": (-1, 1),
            "southeast": (1, -1), "southwest": (-1, -1)
        }
        dx, dy = direction_shifts.get(from_exit.key, (0, 1))
        new_x, new_y = (x + dx, y + dy)
        
        # 计算深度(难度)
        depth = int(sqrt(new_x**2 + new_y**2))
        
        # 调用仙侠房间生成器
        new_room = self.xianxia_room_generator(self, depth, (new_x, new_y))
        
        # 记录房间
        self.xy_grid[(new_x, new_y)] = new_room
        
        # 创建返回出口
        back_direction = self.get_reverse_direction(from_exit.key)
        create.create_object(
            XianxiaDungeonExit,
            key=back_direction,
            location=new_room,
            destination=from_exit.location,
            attributes=(("desc", "通往来路的通道。"),)
        )
        
        # 生成新的探索出口
        self.generate_new_exits(new_room, new_x, new_y, back_direction)
        
        # 触发随机事件
        self.trigger_random_event(new_room, depth)
        
        return new_room
        
    def xianxia_room_generator(self, branch, depth, coords):
        """仙侠风格房间生成器"""
        
        # 根据深度确定区域类型
        area_types = {
            (1, 2): "外围山林",
            (3, 4): "洞府通道", 
            (5, 6): "修炼密室",
            (7, 8): "宝物殿堂",
            (9, 15): "禁地核心"
        }
        
        area_type = "禁地核心"  # 默认
        for (min_d, max_d), area in area_types.items():
            if min_d <= depth <= max_d:
                area_type = area
                break
                
        # 房间模板库
        room_templates = {
            "外围山林": [
                ("竹林小径", "青竹摇曳，山风徐来，空气中弥漫着淡淡的灵气。"),
                ("山石洞穴", "天然形成的石洞，岩壁上隐约可见古老的符文痕迹。"),
                ("灵泉池", "清澈的山泉汇聚成池，泉水散发着淡淡的灵光。"),
                ("古树林", "参天古树遮天蔽日，树干上刻着岁月的痕迹。")
            ],
            "洞府通道": [
                ("石室通道", "人工开凿的石室，两侧石壁上镶嵌着夜明珠照明。"),
                ("阵法走廊", "地面刻着复杂的阵法纹路，散发着神秘的光芒。"),
                ("藏书室", "满墙的古籍散发着智慧的光芒，空气中弥漫着墨香。"),
                ("炼器室", "残留的炉火还在燃烧，各种炼器工具散落一地。")
            ],
            "修炼密室": [
                ("静修室", "四壁刻满静心咒文，是修炼的绝佳场所。"),
                ("聚灵阵", "地面刻着聚灵大阵，灵气在此汇聚不散。"),
                ("悟道台", "古朴的石台，曾有高人在此悟道成仙。"),
                ("炼丹房", "丹炉还有余温，空气中飘着淡淡的丹香。")
            ],
            "宝物殿堂": [
                ("宝库", "金光闪闪的宝库，各种珍宝琳琅满目。"),
                ("兵器架", "陈列着各种神兵利器，寒光闪闪。"),
                ("丹药库", "存放着各种珍贵丹药的宝库。"),
                ("功法殿", "收藏着无数功法秘籍的殿堂。")
            ],
            "禁地核心": [
                ("封印之地", "强大的封印阵法笼罩着这里，透着危险的气息。"),
                ("传承殿", "古老的传承之地，蕴含着无上的机缘。"),
                ("BOSS巢穴", "强大存在的栖息地，危机四伏。"),
                ("时空裂缝", "空间不稳定的区域，充满了未知的危险。")
            ]
        }
        
        # 随机选择房间模板
        templates = room_templates.get(area_type, room_templates["外围山林"])
        room_name, room_desc = random.choice(templates)
        
        # 检查是否生成特殊房间
        if depth >= 8 and random.random() < 0.3:  # 30%概率生成BOSS房间
            room_name = f"【{self.realm_type}】守护者巢穴"
            room_desc = f"这里是{self.realm_type}的核心区域，强大的守护者就在这里。"
            
        # 创建房间
        new_room = create.create_object(
            XianxiaDungeonRoom,
            key=room_name,
            attributes=(
                ("desc", room_desc),
                ("xy_coords", coords),
                ("dungeon_branch", branch),
                ("area_type", area_type),
                ("depth", depth),
                ("realm_type", self.realm_type)
            )
        )
        
        # 添加房间标签
        new_room.tags.add(self.key, category="dungeon_room")
        
        return new_room
        
    def generate_new_exits(self, room, x, y, exclude_direction):
        """生成新的探索出口"""
        available_directions = [
            "north", "south", "east", "west", 
            "northeast", "northwest", "southeast", "southwest"
        ]
        
        # 排除返回方向
        if exclude_direction in available_directions:
            available_directions.remove(exclude_direction)
            
        # 随机生成1-3个新出口
        num_exits = random.randint(1, min(3, len(available_directions)))
        selected_directions = random.sample(available_directions, num_exits)
        
        for direction in selected_directions:
            # 检查目标位置是否已有房间
            dx, dy = self.get_direction_shift(direction)
            target_coords = (x + dx, y + dy)
            
            if target_coords not in self.xy_grid and target_coords != (0, 0):
                self.create_out_exit(room, direction)
                # 预占位置
                self.xy_grid[target_coords] = None
                
    def trigger_random_event(self, room, depth):
        """触发随机事件"""
        event_chance = min(0.4, depth * 0.05)  # 深度越深事件概率越高
        
        if random.random() < event_chance:
            events = [
                "灵气暴动", "宝物现世", "机关触发", 
                "妖兽出现", "阵法激活", "灵药生长"
            ]
            
            event = random.choice(events)
            self.special_events.append({
                "event": event,
                "room": room.id,
                "timestamp": datetime.now().isoformat()
            })
            
            # 执行事件效果
            if event == "灵气暴动":
                room.msg_contents("|c突然间，天地灵气暴动，修炼效果大幅提升！|n")
                room.tags.add("spirit_surge", category="room_effect")
                
            elif event == "宝物现世":
                self.spawn_treasure(room, depth)
                
            elif event == "妖兽出现":
                self.spawn_monster(room, depth)
                
    def spawn_treasure(self, room, depth):
        """生成宝物"""
        treasure_types = ["灵石", "丹药", "法宝", "功法", "灵草"]
        treasure_type = random.choice(treasure_types)
        
        treasure_name = f"{treasure_type}(品质{depth})"
        room.msg_contents(f"|y一道光芒闪过，{treasure_name}出现了！|n")
        
        # 这里可以创建实际的宝物对象
        # treasure = create.create_object(XianxiaTreasure, ...)
        
    def spawn_monster(self, room, depth):
        """生成怪物"""
        monster_types = ["妖兽", "傀儡", "剑灵", "阵灵"]
        monster_type = random.choice(monster_types)
        
        monster_name = f"{monster_type}(等级{depth})"
        room.msg_contents(f"|r{monster_name}突然出现，散发着危险的气息！|n")
        
        # 这里可以创建实际的怪物对象
        # monster = create.create_object(XianxiaMonster, ...)
        
    def get_reverse_direction(self, direction):
        """获取相反方向"""
        reverse_map = {
            "north": "south", "south": "north",
            "east": "west", "west": "east",
            "northeast": "southwest", "southwest": "northeast",
            "northwest": "southeast", "southeast": "northwest"
        }
        return reverse_map.get(direction, "back")
        
    def get_direction_shift(self, direction):
        """获取方向的坐标偏移"""
        shifts = {
            "north": (0, 1), "south": (0, -1),
            "east": (1, 0), "west": (-1, 0),
            "northeast": (1, 1), "northwest": (-1, 1),
            "southeast": (1, -1), "southwest": (-1, -1)
        }
        return shifts.get(direction, (0, 0))


# =============================================================================
# 仙侠副本出口
# =============================================================================

class XianxiaDungeonExit(EvAdventureDungeonExit):
    """仙侠副本出口"""
    
    def at_traverse(self, traversing_object, target_location, **kwargs):
        """穿越出口时的处理"""
        dungeon_branch = self.location.db.dungeon_branch
        
        if target_location == self.location:
            # 目标指向自己，需要创建新房间
            self.destination = target_location = dungeon_branch.new_room(self)
            dungeon_branch.register_exit_traversed(self)
            
            # 通知玩家
            traversing_object.msg("|c你踏入了未知的区域...|n")
            
        # 应用房间效果
        if hasattr(target_location, 'apply_room_effects'):
            target_location.apply_room_effects(traversing_object)
            
        super().at_traverse(traversing_object, target_location, **kwargs)


# =============================================================================
# 仙侠副本入口
# =============================================================================

class XianxiaDungeonStartRoom(EvAdventureDungeonStartRoom):
    """仙侠副本入口房间"""
    
    # 副本配置
    recycle_time = 60 * 10  # 10分钟重置
    branch_max_life = 60 * 60 * 2  # 2小时最大生存期
    
    # 可用的秘境类型
    available_realms = AttributeProperty([
        "剑冢", "丹炉洞府", "上古遗迹", "妖兽巢穴", "阵法迷宫"
    ], autocreate=True)
    
    def get_display_footer(self, looker, **kwargs):
        """显示房间底部信息"""
        footer = super().get_display_footer(looker, **kwargs)
        
        xianxia_footer = (
            "\n|y这里是通往神秘秘境的入口。选择不同的方向将进入不同的秘境：|n\n"
            "|c北方 - 剑冢遗迹|n\n"
            "|c东方 - 丹炉洞府|n\n" 
            "|c南方 - 上古遗迹|n\n"
            "|c西方 - 妖兽巢穴|n\n"
            "|r警告：进入秘境后，只有击败守护者或找到出口才能离开！|n"
        )
        
        return footer + xianxia_footer
        
    # 自定义房间生成器
    def room_generator(self, branch, depth, coords):
        """使用仙侠房间生成器"""
        return branch.xianxia_room_generator(branch, depth, coords)


# =============================================================================
# 使用示例
# =============================================================================

def create_xianxia_dungeon_entrance():
    """创建仙侠副本入口的示例函数"""
    
    # 创建入口房间
    entrance = create.create_object(
        XianxiaDungeonStartRoom,
        key="仙侠秘境入口",
        attributes=(
            ("desc", "这里是通往各种神秘秘境的入口，四周弥漫着古老而神秘的气息。"),
        )
    )
    
    # 创建通往不同秘境的出口
    realm_exits = {
        "north": "剑冢",
        "east": "丹炉洞府", 
        "south": "上古遗迹",
        "west": "妖兽巢穴"
    }
    
    for direction, realm_type in realm_exits.items():
        exit_obj = create.create_object(
            "typeclasses.exits.XianxiaDungeonStartRoomExit",
            key=direction,
            location=entrance,
            attributes=(
                ("desc", f"通往{realm_type}的神秘通道。"),
                ("realm_type", realm_type)
            )
        )
        # 初始时出口指向自己，进入时会创建新副本
        exit_obj.destination = entrance
        
    return entrance


# =============================================================================
# 测试和调试函数
# =============================================================================

def test_xianxia_dungeon():
    """测试仙侠副本系统"""
    print("=== 仙侠副本系统测试 ===")
    print("1. 创建副本入口")
    print("2. 玩家进入不同方向的出口")
    print("3. 系统自动生成对应类型的秘境")
    print("4. 房间包含仙侠特色内容和随机事件")
    print("\n使用方法:")
    print("entrance = create_xianxia_dungeon_entrance()")
    print("# 然后玩家可以通过 'north', 'east', 'south', 'west' 进入不同秘境")


if __name__ == "__main__":
    test_xianxia_dungeon()
