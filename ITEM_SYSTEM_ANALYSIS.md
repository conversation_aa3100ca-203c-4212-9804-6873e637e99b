# Evennia道具系统深度分析

## 📋 官方道具系统现状

### ✅ Evennia已实现的道具功能 (完成度: 90%)

#### 🎒 装备系统 (Equipment System)
**完整实现的功能：**
- ✅ **EquipmentHandler** - 完整的装备管理器
- ✅ **WieldLocation枚举** - 装备位置定义
  ```python
  class WieldLocation(Enum):
      BACKPACK = "backpack"
      WEAPON_HAND = "weapon_hand"
      SHIELD_HAND = "shield_hand"
      TWO_HANDS = "two_handed_weapons"
      BODY = "body"  # armor
      HEAD = "head"  # helmets
  ```

- ✅ **智能装备逻辑** - 自动处理装备冲突
  - 双手武器自动卸下单手武器和盾牌
  - 单手武器自动卸下双手武器
  - 装备容量基于体质属性计算

- ✅ **装备属性计算** - 自动计算总护甲值和武器
  ```python
  @property
  def armor(self):
      return sum((
          getattr(slots[WieldLocation.BODY], "armor", 1),
          getattr(slots[WieldLocation.SHIELD_HAND], "armor", 0),
          getattr(slots[WieldLocation.HEAD], "armor", 0),
      ))
  ```

#### 🗡️ 武器系统 (Weapon System)
**完整实现的功能：**
- ✅ **EvAdventureWeapon基类** - 完整的武器框架
- ✅ **武器属性系统** - 攻击类型、防御类型、伤害骰子
- ✅ **武器耐久度** - 质量系统和损坏机制
- ✅ **战斗集成** - 完整的攻击逻辑和伤害计算
- ✅ **暴击系统** - 暴击成功和暴击失败处理

#### 🛡️ 防具系统 (Armor System)
**完整实现的功能：**
- ✅ **EvAdventureArmor基类** - 护甲、盾牌、头盔
- ✅ **防御值计算** - 自动计算总防御
- ✅ **装备位置管理** - 智能装备替换

#### 🧪 消耗品系统 (Consumable System)
**完整实现的功能：**
- ✅ **EvAdventureConsumable基类** - 完整的消耗品框架
- ✅ **使用次数管理** - 自动扣除使用次数
- ✅ **自动销毁** - 用完自动删除
- ✅ **使用前检查** - 距离和可用性验证

#### 🔮 魔法物品系统 (Magic Item System)
**完整实现的功能：**
- ✅ **EvAdventureRuneStone** - 魔法符石基类
- ✅ **法术武器** - 双手魔法武器
- ✅ **魔法属性** - 智力攻击，敏捷防御
- ✅ **法术刷新** - 休息后恢复使用次数

#### 🔨 制作系统 (Crafting System)
**完整实现的功能：**
- ✅ **CraftingRecipe基类** - 完整的配方系统
- ✅ **材料和工具标签** - 基于标签的材料识别
- ✅ **配方验证** - 自动检查材料和工具
- ✅ **批量制作** - 支持多个输出物品
- ✅ **制作流程** - pre_craft, do_craft, post_craft钩子

### ❌ 需要开发的仙侠特色功能

#### 1. 仙侠法宝系统
- ❌ 法宝品级系统（凡品、灵品、宝品、仙品）
- ❌ 法宝认主和绑定机制
- ❌ 法宝升级和进阶系统
- ❌ 法宝特殊技能和效果

#### 2. 丹药系统
- ❌ 丹药品质和效果
- ❌ 炼丹系统和丹炉
- ❌ 药材采集和培育
- ❌ 丹毒和副作用机制

#### 3. 符箓阵法
- ❌ 符箓制作和使用
- ❌ 阵法布置和激活
- ❌ 符箓材料和墨水系统

#### 4. 灵石货币
- ❌ 灵石等级系统
- ❌ 灵石吸收和转换
- ❌ 灵石交易机制

## 🏮 仙侠道具系统设计方案

### 🌟 方案一: 基于官方系统扩展 (推荐)

#### 1. 仙侠法宝基类
```python
class XianxiaTreasure(EvAdventureObject):
    """仙侠法宝基类"""
    
    # 法宝属性
    treasure_grade = AttributeProperty("凡品", autocreate=True)  # 凡品/灵品/宝品/仙品
    treasure_level = AttributeProperty(1, autocreate=True)       # 法宝等级
    spiritual_power = AttributeProperty(100, autocreate=True)    # 灵力值
    max_spiritual_power = AttributeProperty(100, autocreate=True)
    
    # 认主系统
    owner_id = AttributeProperty(None, autocreate=True)          # 主人ID
    recognition_level = AttributeProperty(0, autocreate=True)    # 认主程度
    
    # 法宝技能
    treasure_skills = AttributeProperty([], autocreate=True)     # 法宝技能列表
    
    def recognize_master(self, character):
        """认主过程"""
        if self.owner_id and self.owner_id != character.id:
            character.msg("这件法宝已有主人，无法认主。")
            return False
            
        # 认主成功条件检查
        if character.realm_level < self.treasure_level:
            character.msg("你的修为不足，无法驾驭此法宝。")
            return False
            
        self.owner_id = character.id
        self.recognition_level = 1
        character.msg(f"你成功认主了{self.name}！")
        return True
        
    def upgrade_treasure(self):
        """法宝升级"""
        if self.recognition_level < 10:
            return False
            
        self.treasure_level += 1
        self.max_spiritual_power += 50
        self.spiritual_power = self.max_spiritual_power
        
        # 升级品级
        if self.treasure_level >= 10 and self.treasure_grade == "凡品":
            self.treasure_grade = "灵品"
        elif self.treasure_level >= 20 and self.treasure_grade == "灵品":
            self.treasure_grade = "宝品"
            
        return True
```

#### 2. 仙侠武器系统
```python
class XianxiaWeapon(XianxiaTreasure, EvAdventureWeapon):
    """仙侠武器"""
    
    obj_type = (ObjType.WEAPON, ObjType.MAGIC)
    weapon_type = AttributeProperty("剑", autocreate=True)  # 剑/刀/枪/棍等
    
    # 武器特殊属性
    sharpness = AttributeProperty(1.0, autocreate=True)    # 锋利度
    durability = AttributeProperty(100, autocreate=True)   # 耐久度
    
    def calculate_damage(self, attacker, target):
        """计算伤害，考虑法宝等级和认主程度"""
        base_damage = super().calculate_damage(attacker, target)
        
        # 认主加成
        recognition_bonus = 1.0 + (self.recognition_level * 0.1)
        
        # 品级加成
        grade_bonus = {
            "凡品": 1.0,
            "灵品": 1.5,
            "宝品": 2.0,
            "仙品": 3.0
        }.get(self.treasure_grade, 1.0)
        
        return int(base_damage * recognition_bonus * grade_bonus)
```

#### 3. 丹药系统
```python
class XianxiaPill(EvAdventureConsumable):
    """仙侠丹药"""
    
    obj_type = ObjType.CONSUMABLE
    pill_type = AttributeProperty("疗伤丹", autocreate=True)
    pill_grade = AttributeProperty("下品", autocreate=True)  # 下品/中品/上品/极品
    
    # 丹药效果
    healing_power = AttributeProperty(50, autocreate=True)
    spiritual_recovery = AttributeProperty(30, autocreate=True)
    side_effects = AttributeProperty([], autocreate=True)
    
    def use(self, user, *args, **kwargs):
        """使用丹药"""
        if not self.check_compatibility(user):
            return False
            
        # 应用丹药效果
        self.apply_effects(user)
        
        # 检查副作用
        self.check_side_effects(user)
        
        return True
        
    def apply_effects(self, user):
        """应用丹药效果"""
        # 恢复生命值
        if self.healing_power > 0:
            user.heal(self.healing_power)
            user.msg(f"服用{self.name}后，你感到伤势快速愈合。")
            
        # 恢复灵力
        if self.spiritual_recovery > 0:
            user.spiritual_power = min(
                user.spiritual_power + self.spiritual_recovery,
                user.spiritual_power_max
            )
            user.msg(f"丹药的药力滋养着你的丹田。")
            
    def check_side_effects(self, user):
        """检查副作用"""
        if self.side_effects and random.random() < 0.1:  # 10%概率
            effect = random.choice(self.side_effects)
            user.msg(f"丹药的副作用显现：{effect}")
```

#### 4. 炼制系统
```python
class XianxiaCraftingRecipe(CraftingRecipe):
    """仙侠炼制配方"""
    
    # 炼制类型
    craft_type = "alchemy"  # alchemy/forging/talisman
    required_realm = "练气期"
    required_level = 1
    
    # 成功率
    base_success_rate = 0.7
    
    def pre_craft(self, crafter, **kwargs):
        """炼制前检查"""
        # 检查修为
        if crafter.realm != self.required_realm or crafter.realm_level < self.required_level:
            raise CraftingValidationError("修为不足，无法炼制此物。")
            
        # 检查炼制设施
        if not self.check_crafting_facility(crafter):
            raise CraftingValidationError("缺少必要的炼制设施。")
            
        return True
        
    def calculate_success_rate(self, crafter):
        """计算成功率"""
        base_rate = self.base_success_rate
        
        # 修为加成
        realm_bonus = crafter.realm_level * 0.05
        
        # 技能加成
        skill_bonus = getattr(crafter, f"{self.craft_type}_skill", 0) * 0.02
        
        return min(0.95, base_rate + realm_bonus + skill_bonus)
        
    def do_craft(self, crafter, **kwargs):
        """执行炼制"""
        success_rate = self.calculate_success_rate(crafter)
        
        if random.random() < success_rate:
            # 成功
            result = super().do_craft(crafter, **kwargs)
            crafter.msg("炼制成功！")
            return result
        else:
            # 失败
            crafter.msg("炼制失败，材料损失。")
            return None
```

### 🌟 方案二: 灵石货币系统

#### 1. 灵石基类
```python
class SpiritStone(EvAdventureObject):
    """灵石货币"""
    
    obj_type = ObjType.TREASURE
    stone_grade = AttributeProperty("下品", autocreate=True)  # 下品/中品/上品/极品
    spiritual_energy = AttributeProperty(1, autocreate=True)  # 灵气值
    
    # 灵石等级对应的灵气值
    GRADE_VALUES = {
        "下品": 1,
        "中品": 10,
        "上品": 100,
        "极品": 1000
    }
    
    def at_object_creation(self):
        super().at_object_creation()
        self.spiritual_energy = self.GRADE_VALUES.get(self.stone_grade, 1)
        
    def absorb_energy(self, character):
        """吸收灵石能量"""
        if character.spiritual_power >= character.spiritual_power_max:
            character.msg("你的灵力已满，无法吸收更多灵气。")
            return False
            
        absorbed = min(
            self.spiritual_energy,
            character.spiritual_power_max - character.spiritual_power
        )
        
        character.spiritual_power += absorbed
        character.msg(f"你吸收了{absorbed}点灵气。")
        
        # 消耗灵石
        self.delete()
        return True
```

## 📊 开发优先级和时间估算

### 🎯 第一优先级 (2-3周)
1. **仙侠法宝基类** - 扩展官方装备系统
2. **基础丹药系统** - 基于消耗品系统
3. **灵石货币** - 简单的货币替代

### 🎯 第二优先级 (3-4周)
4. **法宝升级系统** - 认主和进阶机制
5. **炼制系统扩展** - 基于官方制作系统
6. **符箓基础** - 特殊消耗品类型

### 🎯 第三优先级 (2-3周)
7. **高级炼制** - 复杂的成功率和品质
8. **阵法系统** - 环境效果和布置
9. **法宝技能** - 特殊能力系统

## 💡 关键优势

### ✅ 强大基础
- **90%的核心功能已实现** - 装备、武器、消耗品、制作系统
- **智能装备管理** - 自动处理装备冲突和容量
- **完整的战斗集成** - 武器伤害和防具防御自动计算
- **成熟的制作框架** - 配方、材料、工具完整支持

### 🚀 快速实现路径
1. **直接扩展** - 继承官方类添加仙侠属性
2. **重用逻辑** - 利用现有的装备和战斗系统
3. **渐进开发** - 先基础功能，再高级特性

### 📈 总体评估
Evennia的道具系统是**最完善的模块之一**，提供了90%以上的核心功能。仙侠特色主要是**内容和属性的扩展**，而不是系统重构。

**预计开发时间：7-10周**
- 基础仙侠道具：2-3周
- 高级功能：3-4周  
- 特色系统：2-3周

这使得道具系统成为**最容易实现**的模块，可以作为第一阶段开发的重点内容。
