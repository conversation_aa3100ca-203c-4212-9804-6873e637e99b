/*
 * 仙侠界面增强插件
 * 在现有界面基础上添加仙侠风格元素
 */

let xianxia_ui_enhancer = (function () {
    
    let isInitialized = false;
    let characterStats = {
        realm: "练气期",
        realm_level: 1,
        spiritual_power: 100,
        spiritual_power_max: 100,
        cultivation_exp: 0,
        cultivation_exp_needed: 1000,
        sect: "散修",
        sect_rank: "外门弟子"
    };
    
    // 创建仙侠状态面板
    function createStatsPanel() {
        const panel = $(`
            <div id="xianxia-stats-panel" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 280px;
                background: linear-gradient(145deg, #2c1810, #3d2317);
                border: 2px solid #8B4513;
                border-radius: 8px;
                padding: 15px;
                color: #F5DEB3;
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
                z-index: 1000;
                box-shadow: 0 0 20px rgba(139, 69, 19, 0.5);
            ">
                <div style="
                    color: #FFD700;
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    border-bottom: 1px solid #DAA520;
                    padding-bottom: 5px;
                ">🏮 修仙状态 🏮</div>
                
                <div id="realm-info" style="
                    text-align: center;
                    background: rgba(255, 215, 0, 0.1);
                    border: 1px solid #DAA520;
                    border-radius: 4px;
                    padding: 8px;
                    margin-bottom: 10px;
                ">
                    <div style="color: #FFD700; font-weight: bold;" id="realm-display">练气期1层</div>
                    <div style="color: #F5DEB3; font-size: 11px;" id="sect-display">散修 (外门弟子)</div>
                </div>
                
                <div style="margin: 8px 0;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                        <span>灵力</span>
                        <span id="sp-text">100/100</span>
                    </div>
                    <div style="
                        width: 100%;
                        height: 12px;
                        background: rgba(0, 0, 0, 0.6);
                        border: 1px solid #8B4513;
                        border-radius: 6px;
                        overflow: hidden;
                    ">
                        <div id="sp-bar" style="
                            height: 100%;
                            background: linear-gradient(90deg, #4169E1, #87CEEB);
                            width: 100%;
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                </div>
                
                <div style="margin: 8px 0;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                        <span>修炼进度</span>
                        <span id="cult-text">0/1000</span>
                    </div>
                    <div style="
                        width: 100%;
                        height: 12px;
                        background: rgba(0, 0, 0, 0.6);
                        border: 1px solid #8B4513;
                        border-radius: 6px;
                        overflow: hidden;
                    ">
                        <div id="cult-bar" style="
                            height: 100%;
                            background: linear-gradient(90deg, #FFD700, #FFA500);
                            width: 0%;
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                </div>
                
                <div style="
                    margin-top: 10px;
                    padding-top: 10px;
                    border-top: 1px solid #8B4513;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                ">
                    <button onclick="executeXianxiaCommand('状态')" style="
                        background: linear-gradient(145deg, #3d2317, #2c1810);
                        border: 1px solid #DAA520;
                        color: #FFD700;
                        padding: 4px 8px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                        flex: 1;
                        min-width: 45px;
                    ">状态</button>
                    <button onclick="executeXianxiaCommand('修炼')" style="
                        background: linear-gradient(145deg, #3d2317, #2c1810);
                        border: 1px solid #DAA520;
                        color: #FFD700;
                        padding: 4px 8px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                        flex: 1;
                        min-width: 45px;
                    ">修炼</button>
                    <button onclick="executeXianxiaCommand('突破')" style="
                        background: linear-gradient(145deg, #3d2317, #2c1810);
                        border: 1px solid #DAA520;
                        color: #FFD700;
                        padding: 4px 8px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                        flex: 1;
                        min-width: 45px;
                    ">突破</button>
                    <button onclick="executeXianxiaCommand('技能')" style="
                        background: linear-gradient(145deg, #3d2317, #2c1810);
                        border: 1px solid #DAA520;
                        color: #FFD700;
                        padding: 4px 8px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                        flex: 1;
                        min-width: 45px;
                    ">技能</button>
                </div>
                
                <div style="
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    cursor: pointer;
                    color: #FF6347;
                    font-weight: bold;
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 18px;
                    border-radius: 50%;
                    background: rgba(0, 0, 0, 0.3);
                " onclick="toggleStatsPanel()">×</div>
            </div>
        `);
        
        $('body').append(panel);
        
        // 使面板可拖拽
        panel.draggable({
            handle: panel,
            containment: 'window'
        });
    }
    
    // 创建快捷工具栏
    function createToolbar() {
        const toolbar = $(`
            <div id="xianxia-toolbar" style="
                position: fixed;
                bottom: 10px;
                right: 10px;
                background: linear-gradient(145deg, #2c1810, #3d2317);
                border: 2px solid #8B4513;
                border-radius: 6px;
                padding: 8px;
                display: flex;
                gap: 5px;
                z-index: 999;
                box-shadow: 0 0 15px rgba(139, 69, 19, 0.5);
            ">
                <button onclick="toggleStatsPanel()" style="
                    background: linear-gradient(145deg, #3d2317, #2c1810);
                    border: 1px solid #DAA520;
                    color: #FFD700;
                    padding: 6px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                " title="显示/隐藏状态面板">📊</button>
                <button onclick="executeXianxiaCommand('look')" style="
                    background: linear-gradient(145deg, #3d2317, #2c1810);
                    border: 1px solid #DAA520;
                    color: #FFD700;
                    padding: 6px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                " title="查看周围">👁</button>
                <button onclick="executeXianxiaCommand('inventory')" style="
                    background: linear-gradient(145deg, #3d2317, #2c1810);
                    border: 1px solid #DAA520;
                    color: #FFD700;
                    padding: 6px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                " title="查看背包">🎒</button>
                <button onclick="showXianxiaHelp()" style="
                    background: linear-gradient(145deg, #3d2317, #2c1810);
                    border: 1px solid #DAA520;
                    color: #FFD700;
                    padding: 6px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                " title="仙侠帮助">❓</button>
            </div>
        `);
        
        $('body').append(toolbar);
    }
    
    // 应用仙侠主题样式
    function applyXianxiaTheme() {
        // 修改页面背景
        $('body').css({
            'background': 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
            'color': '#e8e8e8'
        });
        
        // 修改主容器样式
        $('#clientwrapper, .lm_root').css({
            'background': 'rgba(0, 0, 0, 0.3)',
            'border': '2px solid #8B4513',
            'border-radius': '8px',
            'box-shadow': '0 0 20px rgba(139, 69, 19, 0.5)'
        });
        
        // 修改消息窗口样式
        $('.lm_content, #messagewindow').css({
            'background': 'rgba(0, 0, 0, 0.8)',
            'color': '#F5DEB3',
            'font-family': "'Microsoft YaHei', 'Courier New', monospace"
        });
        
        // 修改输入框样式
        $('.inputfield, input[type="text"]').css({
            'background': 'rgba(20, 20, 40, 0.9)',
            'border': '2px solid #DAA520',
            'border-radius': '4px',
            'color': '#FFD700',
            'font-family': "'Microsoft YaHei', sans-serif"
        });
        
        // 修改标签页样式
        $('.lm_tab').css({
            'background': 'rgba(139, 69, 19, 0.3)',
            'border': '1px solid #8B4513',
            'color': '#F5DEB3'
        });
        
        $('.lm_tab.lm_active').css({
            'background': 'rgba(255, 215, 0, 0.2)',
            'color': '#FFD700',
            'border-color': '#DAA520'
        });
    }
    
    // 解析状态消息
    function parseStatusMessage(message) {
        if (message.includes("=== 修仙状态 ===")) {
            const lines = message.split('\n');
            
            lines.forEach(line => {
                line = line.trim();
                if (line.includes("境界：")) {
                    const match = line.match(/境界：(.+?)(\d+)层/);
                    if (match) {
                        characterStats.realm = match[1];
                        characterStats.realm_level = parseInt(match[2]);
                        $('#realm-display').text(`${match[1]}${match[2]}层`);
                    }
                } else if (line.includes("门派：")) {
                    const match = line.match(/门派：(.+?)\s*\((.+?)\)/);
                    if (match) {
                        characterStats.sect = match[1];
                        characterStats.sect_rank = match[2];
                        $('#sect-display').text(`${match[1]} (${match[2]})`);
                    }
                } else if (line.includes("灵力：")) {
                    const match = line.match(/灵力：(\d+)\/(\d+)/);
                    if (match) {
                        characterStats.spiritual_power = parseInt(match[1]);
                        characterStats.spiritual_power_max = parseInt(match[2]);
                        const percent = (characterStats.spiritual_power / characterStats.spiritual_power_max * 100);
                        $('#sp-text').text(`${match[1]}/${match[2]}`);
                        $('#sp-bar').css('width', percent + '%');
                    }
                } else if (line.includes("修炼进度：")) {
                    const match = line.match(/修炼进度：(\d+)\/(\d+)/);
                    if (match) {
                        characterStats.cultivation_exp = parseInt(match[1]);
                        characterStats.cultivation_exp_needed = parseInt(match[2]);
                        const percent = (characterStats.cultivation_exp / characterStats.cultivation_exp_needed * 100);
                        $('#cult-text').text(`${match[1]}/${match[2]}`);
                        $('#cult-bar').css('width', percent + '%');
                        
                        // 如果经验满了，添加发光效果
                        if (percent >= 100) {
                            $('#cult-bar').css('box-shadow', '0 0 10px #FFD700');
                        } else {
                            $('#cult-bar').css('box-shadow', 'none');
                        }
                    }
                }
            });
        }
    }
    
    // 处理文本消息
    var onText = function (args, kwargs) {
        const message = args[0];
        
        // 解析状态消息
        parseStatusMessage(message);
        
        // 检查修炼经验获得
        if (message.includes("修炼完成，获得") && message.includes("点修炼经验")) {
            const match = message.match(/获得(\d+)点修炼经验/);
            if (match) {
                const exp = parseInt(match[1]);
                characterStats.cultivation_exp = Math.min(
                    characterStats.cultivation_exp + exp, 
                    characterStats.cultivation_exp_needed
                );
                const percent = (characterStats.cultivation_exp / characterStats.cultivation_exp_needed * 100);
                $('#cult-text').text(`${characterStats.cultivation_exp}/${characterStats.cultivation_exp_needed}`);
                $('#cult-bar').css('width', percent + '%');
            }
        }
        
        return true;
    };
    
    // 初始化插件
    var init = function () {
        if (isInitialized) return;
        
        console.log('仙侠界面增强插件已加载');
        
        // 等待页面完全加载
        setTimeout(() => {
            applyXianxiaTheme();
            createStatsPanel();
            createToolbar();
            
            // 添加全局函数
            window.executeXianxiaCommand = function(cmd) {
                if (window.Evennia && window.Evennia.msg) {
                    window.Evennia.msg("text", [cmd], {});
                }
            };
            
            window.toggleStatsPanel = function() {
                $('#xianxia-stats-panel').toggle();
            };
            
            window.showXianxiaHelp = function() {
                const helpMsg = `
仙侠MUD 快速帮助：
• 状态 - 查看角色状态
• 修炼 - 开始修炼
• 突破 - 尝试突破境界
• 技能 - 查看技能列表
• 法宝 - 查看法宝
• 加入门派 [门派名] - 加入门派
                `;
                alert(helpMsg);
            };
            
            isInitialized = true;
        }, 1000);
    };
    
    return {
        init: init,
        onText: onText
    };
})();

// 注册插件
window.plugin_handler.add('xianxia_ui_enhancer', xianxia_ui_enhancer);
