"""
仙侠MUD角色系统示例代码
这个文件展示了如何扩展Evennia的角色系统来支持仙侠元素

使用方法:
1. 将此代码集成到 mygame/typeclasses/characters.py
2. 在settings.py中设置 BASE_CHARACTER_TYPECLASS = "typeclasses.characters.XianxiaCharacter"
3. 重启服务器并创建新角色测试
"""

from evennia import DefaultCharacter, AttributeProperty
from evennia.utils import lazy_property
import random
from datetime import datetime, timedelta


class XianxiaCharacter(DefaultCharacter):
    """
    仙侠修士角色类
    扩展了基础角色功能，添加了修仙相关的属性和方法
    """
    
    # =============================================================================
    # 修炼相关属性
    # =============================================================================
    
    # 境界系统
    realm = AttributeProperty("练气期", autocreate=True)
    realm_level = AttributeProperty(1, autocreate=True)  # 境界层次 (1-9层)
    
    # 灵力系统
    spiritual_power = AttributeProperty(100, autocreate=True)
    spiritual_power_max = AttributeProperty(100, autocreate=True)
    spiritual_power_regen = AttributeProperty(1, autocreate=True)  # 每分钟恢复量
    
    # 资质属性
    spiritual_root = AttributeProperty("五行杂灵根", autocreate=True)
    comprehension = AttributeProperty(50, autocreate=True)  # 悟性 (1-100)
    luck = AttributeProperty(50, autocreate=True)  # 气运 (1-100)
    
    # 修炼进度
    cultivation_progress = AttributeProperty(0, autocreate=True)  # 当前境界修炼进度
    cultivation_progress_max = AttributeProperty(1000, autocreate=True)  # 突破所需进度
    
    # 门派信息
    sect = AttributeProperty("散修", autocreate=True)
    master = AttributeProperty(None, autocreate=True)
    disciples = AttributeProperty([], autocreate=True)
    sect_contribution = AttributeProperty(0, autocreate=True)
    
    # 战斗属性
    attack_power = AttributeProperty(10, autocreate=True)
    defense_power = AttributeProperty(10, autocreate=True)
    speed = AttributeProperty(10, autocreate=True)
    
    # 财富
    spirit_stones = AttributeProperty(100, autocreate=True)  # 灵石
    
    # 修炼时间记录
    last_cultivation_time = AttributeProperty(None, autocreate=True)
    total_cultivation_time = AttributeProperty(0, autocreate=True)  # 总修炼时间(分钟)
    
    # =============================================================================
    # 境界系统常量
    # =============================================================================
    
    REALMS = [
        "练气期", "筑基期", "金丹期", "元婴期", 
        "化神期", "合体期", "大乘期", "渡劫期", "大罗金仙"
    ]
    
    SPIRITUAL_ROOTS = [
        "天灵根", "异灵根", "双灵根", "三灵根", 
        "四灵根", "五行杂灵根", "废灵根"
    ]
    
    CULTIVATION_MULTIPLIERS = {
        "天灵根": 3.0,
        "异灵根": 2.5,
        "双灵根": 2.0,
        "三灵根": 1.5,
        "四灵根": 1.2,
        "五行杂灵根": 1.0,
        "废灵根": 0.5
    }
    
    def at_object_creation(self):
        """
        角色创建时的初始化
        """
        super().at_object_creation()
        
        # 随机生成灵根
        self._generate_spiritual_root()
        
        # 根据灵根调整初始属性
        self._adjust_initial_attributes()
        
        # 设置初始描述
        self._set_initial_description()
        
        # 记录创建时间
        self.db.creation_time = datetime.now()
        
    def _generate_spiritual_root(self):
        """随机生成灵根类型"""
        weights = [1, 3, 8, 15, 25, 40, 8]  # 各种灵根的权重
        self.spiritual_root = random.choices(self.SPIRITUAL_ROOTS, weights=weights)[0]
        
    def _adjust_initial_attributes(self):
        """根据灵根调整初始属性"""
        multiplier = self.CULTIVATION_MULTIPLIERS.get(self.spiritual_root, 1.0)
        
        # 调整悟性
        base_comprehension = random.randint(30, 70)
        self.comprehension = min(100, int(base_comprehension * multiplier))
        
        # 调整气运
        self.luck = random.randint(40, 80)
        
        # 调整灵力上限
        self.spiritual_power_max = int(100 * multiplier)
        self.spiritual_power = self.spiritual_power_max
        
    def _set_initial_description(self):
        """设置初始角色描述"""
        desc = f"这是一位{self.realm}{self.realm_level}层的修士，"
        desc += f"拥有{self.spiritual_root}，"
        desc += f"悟性{self.comprehension}，气运{self.luck}。"
        if self.sect != "散修":
            desc += f"现为{self.sect}弟子。"
        else:
            desc += "目前还是散修。"
        self.db.desc = desc
        
    # =============================================================================
    # 修炼相关方法
    # =============================================================================
    
    def start_cultivation(self, duration_minutes=60):
        """
        开始修炼
        
        Args:
            duration_minutes: 修炼时长(分钟)
        """
        if self.last_cultivation_time:
            last_time = datetime.fromisoformat(self.last_cultivation_time)
            if datetime.now() - last_time < timedelta(hours=1):
                return False, "修炼需要间隔至少1小时。"
        
        # 计算修炼效果
        base_progress = duration_minutes * 10
        comprehension_bonus = self.comprehension / 100
        root_multiplier = self.CULTIVATION_MULTIPLIERS.get(self.spiritual_root, 1.0)
        luck_factor = random.uniform(0.8, 1.2) * (self.luck / 100)
        
        total_progress = int(base_progress * comprehension_bonus * root_multiplier * luck_factor)
        
        # 更新修炼进度
        self.cultivation_progress += total_progress
        self.total_cultivation_time += duration_minutes
        self.last_cultivation_time = datetime.now().isoformat()
        
        # 检查是否可以突破
        breakthrough_ready = self.cultivation_progress >= self.cultivation_progress_max
        
        return True, {
            "progress_gained": total_progress,
            "total_progress": self.cultivation_progress,
            "max_progress": self.cultivation_progress_max,
            "breakthrough_ready": breakthrough_ready
        }
        
    def attempt_breakthrough(self):
        """
        尝试境界突破
        """
        if self.cultivation_progress < self.cultivation_progress_max:
            return False, "修炼进度不足，无法突破。"
            
        # 突破成功率计算
        base_chance = 0.7
        comprehension_bonus = (self.comprehension - 50) / 100 * 0.2
        luck_bonus = (self.luck - 50) / 100 * 0.1
        
        success_chance = base_chance + comprehension_bonus + luck_bonus
        success_chance = max(0.1, min(0.95, success_chance))  # 限制在10%-95%
        
        if random.random() < success_chance:
            return self._breakthrough_success()
        else:
            return self._breakthrough_failure()
            
    def _breakthrough_success(self):
        """突破成功"""
        old_realm = f"{self.realm}{self.realm_level}层"
        
        if self.realm_level < 9:
            # 境界层次提升
            self.realm_level += 1
        else:
            # 大境界突破
            current_index = self.REALMS.index(self.realm)
            if current_index < len(self.REALMS) - 1:
                self.realm = self.REALMS[current_index + 1]
                self.realm_level = 1
            else:
                return False, "已达到最高境界！"
                
        # 重置修炼进度
        self.cultivation_progress = 0
        self.cultivation_progress_max = int(self.cultivation_progress_max * 1.5)
        
        # 提升属性
        self._improve_attributes_on_breakthrough()
        
        new_realm = f"{self.realm}{self.realm_level}层"
        return True, f"突破成功！从{old_realm}突破到{new_realm}！"
        
    def _breakthrough_failure(self):
        """突破失败"""
        # 失败惩罚
        lost_progress = int(self.cultivation_progress * 0.3)
        self.cultivation_progress -= lost_progress
        
        return False, f"突破失败！损失修炼进度{lost_progress}点。"
        
    def _improve_attributes_on_breakthrough(self):
        """突破时提升属性"""
        # 灵力上限提升
        self.spiritual_power_max += random.randint(20, 50)
        self.spiritual_power = self.spiritual_power_max
        
        # 战斗属性提升
        self.attack_power += random.randint(5, 15)
        self.defense_power += random.randint(5, 15)
        self.speed += random.randint(3, 10)
        
        # 灵力恢复速度提升
        self.spiritual_power_regen += 1
        
    # =============================================================================
    # 显示方法
    # =============================================================================
    
    def get_cultivation_status(self):
        """获取修炼状态信息"""
        status = f"""
|c修炼状态|n
境界: {self.realm} {self.realm_level}层
灵根: {self.spiritual_root}
悟性: {self.comprehension}/100
气运: {self.luck}/100
灵力: {self.spiritual_power}/{self.spiritual_power_max}
修炼进度: {self.cultivation_progress}/{self.cultivation_progress_max}
门派: {self.sect}
灵石: {self.spirit_stones}
总修炼时间: {self.total_cultivation_time}分钟
        """
        return status.strip()
        
    def get_combat_stats(self):
        """获取战斗属性"""
        stats = f"""
|r战斗属性|n
攻击力: {self.attack_power}
防御力: {self.defense_power}
速度: {self.speed}
灵力: {self.spiritual_power}/{self.spiritual_power_max}
        """
        return stats.strip()
        
    # =============================================================================
    # 重写基础方法
    # =============================================================================
    
    def return_appearance(self, looker, **kwargs):
        """
        重写外观显示，添加修炼信息
        """
        # 获取基础外观
        text = super().return_appearance(looker, **kwargs)
        
        # 添加修炼信息
        cultivation_info = f"\n|c[{self.realm} {self.realm_level}层]|n"
        if self.sect != "散修":
            cultivation_info += f" |g{self.sect}弟子|n"
        
        # 在描述后添加修炼信息
        if "\n" in text:
            lines = text.split("\n")
            lines.insert(1, cultivation_info)
            text = "\n".join(lines)
        else:
            text += cultivation_info
            
        return text


# =============================================================================
# 使用示例和测试代码
# =============================================================================

def test_xianxia_character():
    """
    测试仙侠角色系统的示例代码
    可以在Evennia shell中运行: py exec(open('XIANXIA_CHARACTER_EXAMPLE.py').read()); test_xianxia_character()
    """
    print("=== 仙侠角色系统测试 ===")
    
    # 这里只是示例，实际使用时角色会通过游戏命令创建
    print("1. 角色创建时会随机生成灵根和属性")
    print("2. 使用 practice 命令开始修炼")
    print("3. 使用 breakthrough 命令尝试突破")
    print("4. 使用 cultivation 命令查看修炼状态")
    print("\n建议的下一步:")
    print("- 将此代码集成到角色系统中")
    print("- 创建对应的游戏命令")
    print("- 添加门派系统")
    print("- 实现法术战斗系统")
