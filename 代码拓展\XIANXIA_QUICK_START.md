# 仙侠MUD快速开始指南

## 🚀 立即开始开发

### 第一步: 集成仙侠角色系统

1. **备份当前角色文件**:
```powershell
copy typeclasses\characters.py typeclasses\characters_backup.py
```

2. **修改角色类**:
```python
# 在 typeclasses/characters.py 中添加
from XIANXIA_CHARACTER_EXAMPLE import XianxiaCharacter

class Character(XianxiaCharacter):
    """
    继承仙侠角色系统
    """
    pass
```

3. **重启服务器**:
```powershell
python -m evennia reload
```

### 第二步: 创建基础修炼命令

在 `commands/command.py` 中添加:

```python
class CmdCultivation(Command):
    """
    查看修炼状态
    
    用法: cultivation
    """
    key = "cultivation"
    aliases = ["修炼状态", "xiulian"]
    
    def func(self):
        if hasattr(self.caller, 'get_cultivation_status'):
            self.caller.msg(self.caller.get_cultivation_status())
        else:
            self.caller.msg("你还不是修士。")

class CmdPractice(Command):
    """
    开始修炼
    
    用法: practice [时长(分钟)]
    """
    key = "practice"
    aliases = ["打坐", "dazuo"]
    
    def func(self):
        if not self.args:
            duration = 60  # 默认1小时
        else:
            try:
                duration = int(self.args)
                if duration <= 0 or duration > 480:  # 最多8小时
                    self.caller.msg("修炼时长必须在1-480分钟之间。")
                    return
            except ValueError:
                self.caller.msg("请输入有效的时长(分钟)。")
                return
                
        if hasattr(self.caller, 'start_cultivation'):
            success, result = self.caller.start_cultivation(duration)
            if success:
                progress = result['progress_gained']
                total = result['total_progress']
                max_prog = result['max_progress']
                self.caller.msg(f"修炼{duration}分钟，获得进度{progress}点。")
                self.caller.msg(f"当前进度: {total}/{max_prog}")
                if result['breakthrough_ready']:
                    self.caller.msg("|y你感觉可以尝试突破了！使用 breakthrough 命令。|n")
            else:
                self.caller.msg(result)
        else:
            self.caller.msg("你还不是修士。")

class CmdBreakthrough(Command):
    """
    尝试境界突破
    
    用法: breakthrough
    """
    key = "breakthrough"
    aliases = ["突破", "tupo"]
    
    def func(self):
        if hasattr(self.caller, 'attempt_breakthrough'):
            success, message = self.caller.attempt_breakthrough()
            if success:
                self.caller.msg(f"|g{message}|n")
                # 通知房间内其他人
                self.caller.location.msg_contents(
                    f"{self.caller.name}身上散发出强大的灵力波动！",
                    exclude=self.caller
                )
            else:
                self.caller.msg(f"|r{message}|n")
        else:
            self.caller.msg("你还不是修士。")
```

### 第三步: 添加命令到命令集

在 `commands/default_cmdsets.py` 中:

```python
from .command import CmdCultivation, CmdPractice, CmdBreakthrough

class CharacterCmdSet(default_cmds.CharacterCmdSet):
    def at_cmdset_creation(self):
        super().at_cmdset_creation()
        # 添加仙侠命令
        self.add(CmdCultivation())
        self.add(CmdPractice())
        self.add(CmdBreakthrough())
```

### 第四步: 测试系统

1. **重启服务器**:
```powershell
python -m evennia reload
```

2. **创建测试角色**:
- 登录游戏 (http://localhost:4001)
- 创建新角色
- 使用命令测试:
  - `cultivation` - 查看修炼状态
  - `practice 30` - 修炼30分钟
  - `breakthrough` - 尝试突破

## 🎯 快速验证清单

- [ ] 角色创建时显示灵根信息
- [ ] `cultivation` 命令显示修炼状态
- [ ] `practice` 命令可以增加修炼进度
- [ ] `breakthrough` 命令可以提升境界
- [ ] `look` 命令显示角色境界信息

## 🔄 下一步扩展

### 1. 添加门派系统
```python
class CmdJoinSect(Command):
    key = "join"
    aliases = ["拜师", "入门"]
    # 实现门派加入逻辑
```

### 2. 添加法术系统
```python
class CmdCastSpell(Command):
    key = "cast"
    aliases = ["施法", "法术"]
    # 实现法术释放逻辑
```

### 3. 添加物品系统
```python
class SpiritStone(DefaultObject):
    """灵石物品"""
    pass

class MagicWeapon(DefaultObject):
    """法宝武器"""
    pass
```

## 🐛 常见问题

### Q: 角色没有仙侠属性？
A: 确保已经重启服务器，并且新创建的角色才会有仙侠属性。

### Q: 命令不存在？
A: 检查是否正确添加到了 CharacterCmdSet 中，并重启服务器。

### Q: 修炼没有效果？
A: 检查角色是否继承了 XianxiaCharacter 类。

## 📚 学习资源

- **Evennia官方文档**: https://www.evennia.com/docs/
- **Python教程**: 学习Python基础语法
- **Django教程**: 了解数据库模型设计
- **仙侠小说**: 了解仙侠世界观和设定

## 🎮 游戏设计建议

1. **平衡性**: 确保不同灵根的角色都有游戏乐趣
2. **进度感**: 设计合理的成长曲线
3. **社交性**: 鼓励玩家间的互动和合作
4. **持续性**: 设计长期目标和内容更新

---
**开始你的仙侠MUD开发之旅吧！** 🌟
