|Portal| 2025-06-27 21:17:21 [..] Loaded.
|Portal| 2025-06-27 21:17:18 [..] Loading C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\evennia\server\portal\portal.py...
|Portal| 2025-06-27 21:17:21 [..] Loaded.
|Portal| 2025-06-27 21:17:21 [..] twistd 23.10.0 (C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe 3.11.4) starting up.
|Portal| 2025-06-27 21:17:21 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
|Portal| 2025-06-27 21:17:21 [..] AMP starting on 4006
|Portal| 2025-06-27 21:17:21 [..] Telnet starting on 4000
|Portal| 2025-06-27 21:17:21 [..] Websocket starting on 4002
|Portal| 2025-06-27 21:17:21 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-27 21:17:21 [..] Portal starting server ... 
|Portal| 2025-06-27 21:17:26 [..] Portal starting server ... 
|Portal| 2025-06-27 21:18:48 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10086)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10124)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10109)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10166)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10167)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10168)
|Portal| 2025-06-27 21:19:07 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10110)
