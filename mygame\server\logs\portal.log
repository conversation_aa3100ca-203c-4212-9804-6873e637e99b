|Portal| 2025-06-27 21:17:21 [..] Loaded.
|Portal| 2025-06-27 21:17:18 [..] Loading C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\evennia\server\portal\portal.py...
|Portal| 2025-06-27 21:17:21 [..] Loaded.
|Portal| 2025-06-27 21:17:21 [..] twistd 23.10.0 (C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe 3.11.4) starting up.
|Portal| 2025-06-27 21:17:21 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
|Portal| 2025-06-27 21:17:21 [..] AMP starting on 4006
|Portal| 2025-06-27 21:17:21 [..] Telnet starting on 4000
|Portal| 2025-06-27 21:17:21 [..] Websocket starting on 4002
|Portal| 2025-06-27 21:17:21 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-27 21:17:21 [..] Portal starting server ... 
|Portal| 2025-06-27 21:17:26 [..] Portal starting server ... 
|Portal| 2025-06-27 21:18:48 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10086)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10124)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10109)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10166)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10167)
|Portal| 2025-06-27 21:19:01 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10168)
|Portal| 2025-06-27 21:19:07 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10110)
|Portal| 2025-06-27 21:21:36 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10309)
|Portal| 2025-06-27 21:21:36 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10308)
|Portal| 2025-06-27 21:21:36 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10314)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10427)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10425)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10458)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10460)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10459)
|Portal| 2025-06-27 21:22:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=10461)
|Portal| 2025-06-27 22:52:08 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3603)
|Portal| 2025-06-27 22:52:08 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3637)
|Portal| 2025-06-27 22:52:08 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3638)
|Portal| 2025-06-27 22:52:08 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3639)
|Portal| 2025-06-27 22:52:08 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3640)
|Portal| 2025-06-27 22:52:15 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=3601)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4898)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4899)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4933)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4935)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4934)
|Portal| 2025-06-27 23:04:42 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=4936)
|Portal| 2025-06-27 23:08:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5307)
|Portal| 2025-06-27 23:08:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5311)
|Portal| 2025-06-27 23:08:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5312)
|Portal| 2025-06-27 23:10:41 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5484)
|Portal| 2025-06-27 23:10:41 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5612)
|Portal| 2025-06-27 23:10:41 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5613)
|Portal| 2025-06-27 23:10:41 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5615)
|Portal| 2025-06-27 23:10:41 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5614)
|Portal| 2025-06-27 23:10:44 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=5295)
|Portal| 2025-06-27 23:17:03 [..] Portal starting server ... 
