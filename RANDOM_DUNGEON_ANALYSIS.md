# Evennia随机地图和副本系统深度分析

## 📋 官方文档发现

### ✅ Evennia已有的随机地图系统

经过深入研究官方文档，我发现Evennia已经有一套**完整的随机地图生成系统**！

#### 🏗️ 核心系统架构

1. **EvAdventureDungeonBranch** - 副本实例管理器
   ```python
   class EvAdventureDungeonBranch(DefaultScript):
       """管理单个副本实例的脚本"""
       max_unexplored_exits = 2      # 最大未探索出口数
       max_new_exits_per_room = 2    # 每房间最大新出口数
       xy_grid = AttributeProperty(dict())  # 坐标网格
       room_generator = AttributeProperty(None)  # 房间生成器
   ```

2. **EvAdventureDungeonStartRoom** - 副本入口
   ```python
   class EvAdventureDungeonStartRoom(EvAdventureDungeonRoom):
       """副本起始房间，管理多个副本分支"""
       recycle_time = 60 * 5         # 5分钟重置时间
       branch_max_life = 60 * 60 * 24 * 7  # 1周最大生存期
   ```

3. **动态房间生成** - 按需创建
   ```python
   def new_room(self, from_exit):
       """从指定出口创建新房间"""
       # 计算新房间坐标
       # 确定深度(难度等级)
       # 调用房间生成器
       # 创建返回出口
       # 生成新的未探索出口
   ```

### 🎯 系统特色功能

#### 1. **程序化生成**
- **坐标系统**: 使用(x,y)坐标追踪房间位置
- **深度计算**: `depth = sqrt(x² + y²)` 确定难度
- **方向映射**: 四个基本方向的坐标偏移

#### 2. **实例化副本**
- **分支隔离**: 每个玩家组获得独立副本
- **标签系统**: 使用`dungeon_character`标签追踪玩家
- **自动清理**: 超时自动删除副本和传送玩家

#### 3. **智能出口管理**
- **预算控制**: 限制未探索出口数量防止指数增长
- **单向连接**: 避免回到起始房间的连接
- **动态创建**: 只在玩家穿越时创建目标房间

## 🏮 仙侠副本系统设计方案

### 🌟 方案一: 基于官方系统扩展

#### 1. 仙侠房间生成器
```python
def xianxia_room_generator(dungeon_branch, depth, coords):
    """仙侠风格房间生成器"""
    
    # 根据深度确定区域类型
    area_types = {
        1-2: "外围山林",
        3-4: "洞府通道", 
        5-6: "修炼密室",
        7-8: "宝物殿堂",
        9-10: "禁地核心"
    }
    
    # 随机选择房间模板
    room_templates = {
        "外围山林": [
            ("竹林小径", "青竹摇曳，灵气淡薄。"),
            ("山石洞穴", "岩壁上刻着古老的符文。"),
            ("灵泉池", "清澈的泉水散发着淡淡灵气。")
        ],
        "洞府通道": [
            ("石室通道", "两侧石壁上镶嵌着夜明珠。"),
            ("阵法走廊", "地面刻着复杂的阵法纹路。"),
            ("藏书室", "古老的典籍散发着智慧的光芒。")
        ]
        # ... 更多模板
    }
    
    # 根据坐标和深度生成特殊房间
    if coords == (0, 5):  # 特定坐标的BOSS房间
        return create_boss_room(depth)
    
    # 随机生成普通房间
    area = get_area_by_depth(depth)
    template = random.choice(room_templates[area])
    
    room = create.create_object(
        XianxiaDungeonRoom,
        key=template[0],
        attributes=(
            ("desc", template[1]),
            ("xy_coords", coords),
            ("dungeon_branch", dungeon_branch),
            ("area_type", area),
            ("depth", depth)
        )
    )
    
    # 添加随机遭遇
    add_random_encounters(room, depth)
    
    return room
```

#### 2. 仙侠遭遇系统
```python
def add_random_encounters(room, depth):
    """添加随机遭遇"""
    encounter_chance = min(0.8, depth * 0.1)
    
    if random.random() < encounter_chance:
        encounter_type = random.choice([
            "妖兽", "机关", "宝箱", "灵药", "阵法"
        ])
        
        if encounter_type == "妖兽":
            create_monster(room, depth)
        elif encounter_type == "机关":
            create_trap(room, depth)
        elif encounter_type == "宝箱":
            create_treasure(room, depth)
        # ... 其他遭遇类型
```

#### 3. 副本难度系统
```python
class XianxiaDungeonBranch(EvAdventureDungeonBranch):
    """仙侠副本分支"""
    
    def calculate_difficulty(self, depth):
        """根据深度计算难度"""
        base_difficulty = depth
        
        # 添加随机波动
        variance = random.uniform(0.8, 1.2)
        
        # 考虑玩家等级
        avg_player_level = self.get_average_player_level()
        level_modifier = avg_player_level / 10
        
        return int(base_difficulty * variance * level_modifier)
    
    def get_average_player_level(self):
        """获取副本内玩家平均等级"""
        characters = search.search_object_by_tag(
            self.key, category="dungeon_character"
        )
        if not characters:
            return 1
            
        total_level = sum(char.realm_level for char in characters)
        return total_level / len(characters)
```

### 🌟 方案二: 仙侠秘境系统

#### 1. 秘境类型设计
```python
XIANXIA_SECRET_REALMS = {
    "剑冢": {
        "description": "古代剑修的埋骨之地",
        "room_types": ["剑墓", "剑阵", "剑灵殿"],
        "monsters": ["剑灵", "剑魂", "剑鬼"],
        "treasures": ["古剑", "剑谱", "剑意石"],
        "max_depth": 15
    },
    "丹炉洞府": {
        "description": "炼丹大师的隐秘洞府", 
        "room_types": ["丹房", "药园", "炼器室"],
        "monsters": ["丹火精", "药灵", "器魂"],
        "treasures": ["丹药", "灵草", "法宝"],
        "max_depth": 12
    },
    "上古遗迹": {
        "description": "远古修仙者的遗迹",
        "room_types": ["石殿", "符文室", "传承塔"],
        "monsters": ["石傀儡", "符文兽", "守护灵"],
        "treasures": ["功法", "神通", "仙器"],
        "max_depth": 20
    }
}
```

#### 2. 动态事件系统
```python
class XianxiaSecretRealmEvent:
    """秘境动态事件"""
    
    @staticmethod
    def trigger_random_event(room):
        """触发随机事件"""
        events = [
            "灵气暴动",
            "空间裂缝", 
            "宝物现世",
            "强敌来袭",
            "机缘降临"
        ]
        
        event = random.choice(events)
        
        if event == "灵气暴动":
            # 所有玩家获得修炼加成
            room.msg_contents(
                "|c天地灵气突然暴动，修炼效果翻倍！|n"
            )
            room.tags.add("spirit_surge", category="room_effect")
            
        elif event == "空间裂缝":
            # 创建通往其他区域的临时通道
            create_temporary_portal(room)
            
        # ... 其他事件处理
```

### 🌟 方案三: 混合系统 (推荐)

#### 核心设计理念
- **基础框架**: 使用官方的DungeonBranch系统
- **仙侠扩展**: 添加仙侠特色的房间、怪物、宝物
- **动态内容**: 实现事件系统和特殊机制

#### 实现步骤

1. **扩展房间系统** (1周)
   ```python
   class XianxiaDungeonRoom(EvAdventureDungeonRoom):
       # 添加仙侠特色属性
       spiritual_density = AttributeProperty(1.0)  # 灵气浓度
       feng_shui_type = AttributeProperty("普通")   # 风水类型
       hidden_treasures = AttributeProperty([])    # 隐藏宝物
   ```

2. **创建生成器库** (1周)
   ```python
   # 房间模板库
   ROOM_TEMPLATES = {...}
   # 怪物模板库  
   MONSTER_TEMPLATES = {...}
   # 宝物模板库
   TREASURE_TEMPLATES = {...}
   ```

3. **实现特殊机制** (2周)
   ```python
   # 阵法房间
   class FormationRoom(XianxiaDungeonRoom):
       def solve_formation(self, character):
           # 阵法解谜逻辑
   
   # BOSS房间
   class BossRoom(XianxiaDungeonRoom):
       def spawn_boss(self):
           # BOSS生成逻辑
   ```

## 🛠️ 技术实现细节

### 1. 副本实例管理
```python
class XianxiaInstanceManager:
    """仙侠副本实例管理器"""
    
    @classmethod
    def create_instance(cls, realm_type, players):
        """创建副本实例"""
        instance_key = f"xianxia_{realm_type}_{datetime.now()}"
        
        # 创建副本脚本
        branch = create.create_script(
            XianxiaDungeonBranch,
            key=instance_key,
            attributes=(
                ("realm_type", realm_type),
                ("players", [p.id for p in players]),
                ("difficulty", cls.calculate_difficulty(players))
            )
        )
        
        # 为玩家添加标签
        for player in players:
            player.tags.add(instance_key, category="dungeon_character")
            
        return branch
```

### 2. 保存/加载系统
```python
def save_instance_state(branch):
    """保存副本状态"""
    state = {
        "rooms": [(room.id, room.xy_coords) for room in branch.rooms],
        "player_progress": branch.get_player_progress(),
        "special_events": branch.db.triggered_events,
        "boss_status": branch.db.boss_defeated
    }
    branch.db.saved_state = state

def load_instance_state(branch):
    """加载副本状态"""
    if hasattr(branch.db, 'saved_state'):
        state = branch.db.saved_state
        # 恢复房间状态
        # 恢复玩家进度
        # 恢复事件状态
```

### 3. 性能优化
```python
class LazyRoomLoader:
    """延迟房间加载器"""
    
    def __init__(self, branch):
        self.branch = branch
        self.loaded_rooms = {}
        
    def get_room(self, coords):
        """获取房间，如不存在则创建"""
        if coords not in self.loaded_rooms:
            self.loaded_rooms[coords] = self.create_room(coords)
        return self.loaded_rooms[coords]
        
    def unload_distant_rooms(self, player_coords, max_distance=3):
        """卸载距离玩家较远的房间"""
        for coords, room in list(self.loaded_rooms.items()):
            distance = self.calculate_distance(coords, player_coords)
            if distance > max_distance:
                room.delete()
                del self.loaded_rooms[coords]
```

## 📊 开发时间估算

### 阶段一: 基础系统 (2-3周)
- 扩展官方DungeonBranch系统
- 创建仙侠房间模板
- 实现基础生成器

### 阶段二: 内容丰富 (3-4周)  
- 添加怪物和宝物系统
- 实现特殊房间类型
- 创建BOSS战机制

### 阶段三: 高级功能 (2-3周)
- 动态事件系统
- 副本保存/加载
- 性能优化

### 阶段四: 平衡调优 (1-2周)
- 难度平衡
- 奖励调整
- Bug修复

## 🎯 推荐实施路径

1. **立即开始**: 研究官方DungeonBranch代码
2. **第一步**: 创建简单的仙侠房间生成器
3. **第二步**: 扩展为完整的秘境系统
4. **第三步**: 添加高级功能和优化

这个系统的最大优势是**基于官方已有的成熟框架**，避免了重复造轮子，同时可以充分利用Evennia的强大功能。
