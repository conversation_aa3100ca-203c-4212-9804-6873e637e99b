# Evennia 快速启动指南

## 🎯 一键启动命令

```powershell
# 1. 确保在正确目录
cd d:\game\evennia\mygame

# 2. 启动服务器
python -m evennia start

# 3. 检查状态
python -m evennia status
```

## 🔑 关键解决方案

### 核心问题：typing模块冲突
```powershell
# 如果遇到 AttributeError: type object 'Callable' 错误
pip uninstall typing -y
```

### Windows兼容性
```powershell
# 始终使用这个格式，不要直接用 evennia 命令
python -m evennia [命令]
```

## 📋 访问信息

- **Web界面**: http://localhost:4001
- **Telnet**: localhost:4000  
- **管理员**: admin / AdminPass123!

## 🚨 故障排除

| 错误 | 解决方案 |
|------|----------|
| `no such table: accounts_accountdb` | `python -m evennia migrate` |
| `evennia命令打开编辑器` | 使用 `python -m evennia` |
| `AttributeError: Callable` | `pip uninstall typing -y` |
| `Connection timed out` | 先解决typing冲突 |

## ✅ 验证成功

```powershell
# 应该看到这个输出
Portal: RUNNING (pid XXXX)
Server: RUNNING (pid XXXX)
```

---
**状态**: ✅ 项目运行正常  
**最后更新**: 2025-06-27
