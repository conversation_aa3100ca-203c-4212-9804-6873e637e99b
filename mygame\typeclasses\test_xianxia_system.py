"""
仙侠系统测试脚本

这个脚本用于测试仙侠MUD系统的第一阶段功能：
1. 角色系统测试
2. 道具系统测试
3. 房间系统测试
4. 命令系统测试

运行方法：
在Evennia游戏中执行：
@py exec(open('test_xianxia_system.py').read())
"""

from evennia import create_object, search_object
from evennia.utils import logger
from xianxia import *

def test_character_system():
    """测试角色系统"""
    print("=== 测试角色系统 ===")
    
    try:
        # 创建测试角色
        char = create_object(
            XianxiaCharacter,
            key="测试修士",
            location=None
        )
        
        print(f"✓ 创建角色成功: {char.name}")
        print(f"  境界: {char.realm}{char.realm_level}层")
        print(f"  门派: {char.sect}")
        print(f"  灵力: {char.spiritual_power}/{char.spiritual_power_max}")
        
        # 测试修炼
        print("\n--- 测试修炼系统 ---")
        char.start_cultivation(30)  # 修炼30分钟
        print(f"  修炼经验: {char.cultivation_exp}/{char.cultivation_exp_needed}")
        
        # 测试技能
        print("\n--- 测试技能系统 ---")
        char.learn_skill(XianxiaSkill.SWORD_MASTERY.value)
        char.gain_skill_proficiency(XianxiaSkill.SWORD_MASTERY.value, 50)
        print(f"  技能: {char.get_skills_display()}")
        
        # 测试门派
        print("\n--- 测试门派系统 ---")
        char.join_sect(Sect.SWORD_SECT.value)
        print(f"  门派: {char.sect} ({char.sect_rank})")
        
        print("✓ 角色系统测试通过")
        return char
        
    except Exception as e:
        print(f"✗ 角色系统测试失败: {e}")
        return None

def test_object_system():
    """测试道具系统"""
    print("\n=== 测试道具系统 ===")
    
    try:
        # 测试法宝
        print("--- 测试法宝系统 ---")
        treasure = create_object(
            XianxiaTreasure,
            key="测试法宝",
            location=None
        )
        print(f"✓ 创建法宝: {treasure.name}")
        print(f"  品级: {treasure.treasure_grade}")
        print(f"  等级: {treasure.treasure_level}")
        
        # 测试武器
        print("\n--- 测试武器系统 ---")
        weapon = create_object(
            XianxiaWeapon,
            key="青锋剑",
            location=None,
            attributes=[
                ("weapon_type", WeaponType.SWORD.value),
                ("treasure_grade", TreasureGrade.SPIRITUAL.value)
            ]
        )
        print(f"✓ 创建武器: {weapon.name}")
        print(f"  类型: {weapon.weapon_type}")
        print(f"  品级: {weapon.treasure_grade}")
        
        # 测试丹药
        print("\n--- 测试丹药系统 ---")
        pill = create_object(
            XianxiaPill,
            key="疗伤丹",
            location=None,
            attributes=[
                ("pill_type", PillType.HEALING.value),
                ("pill_grade", "中品")
            ]
        )
        print(f"✓ 创建丹药: {pill.name}")
        print(f"  类型: {pill.pill_type}")
        print(f"  品级: {pill.pill_grade}")
        print(f"  效果: 恢复{pill.healing_power}生命值")
        
        # 测试灵石
        print("\n--- 测试灵石系统 ---")
        stone = SpiritStone.create_spirit_stone(
            SpiritStoneGrade.MEDIUM.value,
            location=None
        )
        print(f"✓ 创建灵石: {stone.name}")
        print(f"  品级: {stone.stone_grade}")
        print(f"  价值: {stone.get_value()}")
        
        print("✓ 道具系统测试通过")
        return [treasure, weapon, pill, stone]
        
    except Exception as e:
        print(f"✗ 道具系统测试失败: {e}")
        return []

def test_room_system():
    """测试房间系统"""
    print("\n=== 测试房间系统 ===")
    
    try:
        # 测试普通房间
        print("--- 测试普通房间 ---")
        room = create_object(
            XianxiaRoom,
            key="测试房间",
            location=None
        )
        print(f"✓ 创建房间: {room.name}")
        print(f"  类型: {room.room_type}")
        print(f"  灵气浓度: {room.spiritual_density}")
        print(f"  风水: {room.feng_shui_type}")
        print(f"  修炼加成: {room.cultivation_bonus:.1f}倍")
        
        # 测试修炼室
        print("\n--- 测试修炼室 ---")
        cultivation_room = create_object(
            CultivationRoom,
            key="修炼室",
            location=None
        )
        print(f"✓ 创建修炼室: {cultivation_room.name}")
        print(f"  修炼加成: {cultivation_room.cultivation_bonus:.1f}倍")
        print(f"  突破加成: {cultivation_room.breakthrough_bonus}%")
        
        # 测试炼丹房
        print("\n--- 测试炼丹房 ---")
        alchemy_room = create_object(
            AlchemyRoom,
            key="炼丹房",
            location=None
        )
        print(f"✓ 创建炼丹房: {alchemy_room.name}")
        print(f"  特殊效果: {alchemy_room.special_effects}")
        
        print("✓ 房间系统测试通过")
        return [room, cultivation_room, alchemy_room]
        
    except Exception as e:
        print(f"✗ 房间系统测试失败: {e}")
        return []

def test_integration():
    """测试系统集成"""
    print("\n=== 测试系统集成 ===")
    
    try:
        # 创建测试环境
        char = create_object(XianxiaCharacter, key="集成测试角色")
        room = create_object(CultivationRoom, key="集成测试修炼室")
        weapon = create_object(XianxiaWeapon, key="集成测试剑")
        pill = create_object(XianxiaPill, key="集成测试丹药")
        
        # 将角色和道具放入房间
        char.move_to(room, quiet=True)
        weapon.move_to(char, quiet=True)
        pill.move_to(char, quiet=True)
        
        print("✓ 创建集成测试环境")
        
        # 测试法宝认主
        print("\n--- 测试法宝认主 ---")
        success = weapon.recognize_master(char)
        print(f"  认主结果: {'成功' if success else '失败'}")
        if success:
            print(f"  认主程度: {weapon.recognition_level}/10")
        
        # 测试房间效果
        print("\n--- 测试房间效果 ---")
        effectiveness = room.get_cultivation_effectiveness(char)
        print(f"  修炼效果: {effectiveness:.1f}倍")
        
        # 测试丹药使用
        print("\n--- 测试丹药使用 ---")
        old_hp = getattr(char, 'hp', 100)
        pill.use(char)
        print(f"  丹药使用成功")
        
        print("✓ 系统集成测试通过")
        
        # 清理测试对象
        char.delete()
        room.delete()
        
        return True
        
    except Exception as e:
        print(f"✗ 系统集成测试失败: {e}")
        return False

def test_command_system():
    """测试命令系统"""
    print("\n=== 测试命令系统 ===")
    
    try:
        # 检查命令集
        cmdset = XianxiaCmdSet()
        print(f"✓ 命令集创建成功")
        print(f"  包含命令数量: {len(cmdset.commands)}")
        
        # 列出所有命令
        commands = [cmd.key for cmd in cmdset.commands]
        print(f"  命令列表: {', '.join(commands)}")
        
        print("✓ 命令系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 命令系统测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始仙侠系统第一阶段测试...")
    print("=" * 50)
    
    results = []
    
    # 运行各项测试
    char = test_character_system()
    results.append(char is not None)
    
    objects = test_object_system()
    results.append(len(objects) > 0)
    
    rooms = test_room_system()
    results.append(len(rooms) > 0)
    
    cmd_result = test_command_system()
    results.append(cmd_result)
    
    integration_result = test_integration()
    results.append(integration_result)
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("=== 测试结果汇总 ===")
    
    test_names = [
        "角色系统",
        "道具系统", 
        "房间系统",
        "命令系统",
        "系统集成"
    ]
    
    passed = 0
    for i, result in enumerate(results):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_names[i]}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！仙侠系统第一阶段开发完成！")
    else:
        print("⚠️  部分测试失败，需要修复问题。")
    
    return passed == len(results)

# 运行测试
if __name__ == "__main__":
    run_all_tests()
else:
    # 在Evennia中执行时
    run_all_tests()
