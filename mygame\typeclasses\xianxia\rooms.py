"""
仙侠房间系统
基于Evennia官方Room系统扩展的仙侠房间类

功能包括：
- 仙侠风格的房间描述和环境
- 灵气浓度和风水效果
- 修炼加成和特殊效果
- 随机事件触发
- 门派建筑和特殊房间
"""

from evennia import DefaultRoom, AttributeProperty
from evennia.contrib.tutorials.evadventure.dungeon import EvAdventureDungeonRoom
from enum import Enum
import random
from datetime import datetime


# =============================================================================
# 仙侠房间枚举
# =============================================================================

class RoomType(Enum):
    """房间类型"""
    NORMAL = "普通"
    CULTIVATION = "修炼室"
    ALCHEMY = "炼丹房"
    FORGE = "炼器室"
    LIBRARY = "藏书阁"
    TREASURE = "宝库"
    SECT_HALL = "宗门大殿"
    MARKET = "坊市"
    WILDERNESS = "野外"
    CAVE = "洞府"
    TEMPLE = "神庙"


class SpiritualDensity(Enum):
    """灵气浓度"""
    BARREN = "贫瘠"
    THIN = "稀薄"
    NORMAL = "普通"
    RICH = "浓郁"
    DENSE = "浓密"
    OVERWHELMING = "充盈"


class FengShuiType(Enum):
    """风水类型"""
    NORMAL = "普通"
    GATHERING = "聚灵"
    EVIL = "煞气"
    AUSPICIOUS = "祥瑞"
    DANGEROUS = "凶险"
    BLESSED = "福地"


# =============================================================================
# 仙侠房间基类
# =============================================================================

class XianxiaRoom(DefaultRoom):
    """
    仙侠房间基类
    扩展了Evennia的DefaultRoom，添加仙侠特色功能
    """
    
    # 房间基础属性
    room_type = AttributeProperty(RoomType.NORMAL.value, autocreate=True)
    
    # 灵气和风水属性
    spiritual_density = AttributeProperty(SpiritualDensity.NORMAL.value, autocreate=True)
    spiritual_power = AttributeProperty(100, autocreate=True)  # 房间灵气值
    feng_shui_type = AttributeProperty(FengShuiType.NORMAL.value, autocreate=True)
    
    # 修炼加成
    cultivation_bonus = AttributeProperty(1.0, autocreate=True)  # 修炼效果倍数
    breakthrough_bonus = AttributeProperty(0, autocreate=True)   # 突破成功率加成
    
    # 特殊效果
    special_effects = AttributeProperty([], autocreate=True)
    active_formations = AttributeProperty([], autocreate=True)  # 激活的阵法
    
    # 房间事件
    last_event_time = AttributeProperty(None, autocreate=True)
    event_cooldown = AttributeProperty(3600, autocreate=True)  # 事件冷却时间(秒)
    
    def at_object_creation(self):
        """房间创建时的初始化"""
        super().at_object_creation()
        
        # 随机设置基础属性
        self.randomize_room_properties()
        
        # 添加房间标签
        self.tags.add("xianxia_room", category="room_type")
        self.tags.add(self.room_type, category="room_function")
        
        # 启动房间效果
        self.apply_room_effects()
        
    def randomize_room_properties(self):
        """随机化房间属性"""
        # 随机灵气浓度
        densities = list(SpiritualDensity)
        weights = [5, 15, 40, 25, 10, 5]  # 权重分布
        self.spiritual_density = random.choices(densities, weights=weights)[0].value
        
        # 根据灵气浓度设置灵气值
        density_values = {
            SpiritualDensity.BARREN.value: 20,
            SpiritualDensity.THIN.value: 50,
            SpiritualDensity.NORMAL.value: 100,
            SpiritualDensity.RICH.value: 200,
            SpiritualDensity.DENSE.value: 400,
            SpiritualDensity.OVERWHELMING.value: 800
        }
        self.spiritual_power = density_values.get(self.spiritual_density, 100)
        
        # 随机风水类型
        feng_shui_types = list(FengShuiType)
        feng_shui_weights = [60, 15, 10, 8, 5, 2]  # 普通最常见
        self.feng_shui_type = random.choices(feng_shui_types, weights=feng_shui_weights)[0].value
        
        # 根据风水设置修炼加成
        self.set_cultivation_bonus()
        
    def set_cultivation_bonus(self):
        """根据风水类型设置修炼加成"""
        feng_shui_bonuses = {
            FengShuiType.NORMAL.value: {"cultivation": 1.0, "breakthrough": 0},
            FengShuiType.GATHERING.value: {"cultivation": 1.5, "breakthrough": 10},
            FengShuiType.EVIL.value: {"cultivation": 0.7, "breakthrough": -20},
            FengShuiType.AUSPICIOUS.value: {"cultivation": 1.3, "breakthrough": 15},
            FengShuiType.DANGEROUS.value: {"cultivation": 0.5, "breakthrough": -30},
            FengShuiType.BLESSED.value: {"cultivation": 2.0, "breakthrough": 25}
        }
        
        bonus = feng_shui_bonuses.get(self.feng_shui_type, feng_shui_bonuses[FengShuiType.NORMAL.value])
        self.cultivation_bonus = bonus["cultivation"]
        self.breakthrough_bonus = bonus["breakthrough"]
        
    def apply_room_effects(self):
        """应用房间效果"""
        # 根据房间类型设置特殊效果
        if self.room_type == RoomType.CULTIVATION.value:
            self.cultivation_bonus *= 1.5
            self.special_effects.append("修炼效果提升50%")
            
        elif self.room_type == RoomType.ALCHEMY.value:
            self.special_effects.append("炼丹成功率提升")
            
        elif self.room_type == RoomType.LIBRARY.value:
            self.special_effects.append("学习技能效果提升")
            
    def get_display_header(self, looker, **kwargs):
        """显示房间头部信息"""
        header = super().get_display_header(looker, **kwargs)
        
        # 添加仙侠风格信息
        xianxia_info = f"\n|c[{self.room_type}] 灵气: {self.spiritual_density} | 风水: {self.feng_shui_type}|n"
        
        # 显示修炼加成
        if self.cultivation_bonus != 1.0:
            bonus_text = f"修炼效果: {self.cultivation_bonus:.1f}倍"
            if self.breakthrough_bonus != 0:
                bonus_text += f" | 突破加成: {self.breakthrough_bonus:+d}%"
            xianxia_info += f"|y{bonus_text}|n\n"
            
        return header + xianxia_info
        
    def at_object_receive(self, arriving_obj, source_location, **kwargs):
        """角色进入房间时的处理"""
        super().at_object_receive(arriving_obj, source_location, **kwargs)
        
        # 如果是玩家角色
        if arriving_obj.has_account:
            # 应用房间效果到角色
            self.apply_effects_to_character(arriving_obj)
            
            # 检查随机事件
            self.check_random_event(arriving_obj)
            
    def apply_effects_to_character(self, character):
        """应用房间效果到角色"""
        if not hasattr(character, 'realm'):
            return
            
        # 风水效果
        if self.feng_shui_type == FengShuiType.GATHERING.value:
            character.msg("|g这里灵气聚集，你感到修炼效果会更好。|n")
            
        elif self.feng_shui_type == FengShuiType.EVIL.value:
            character.msg("|r这里煞气逼人，你感到不安。|n")
            
        elif self.feng_shui_type == FengShuiType.AUSPICIOUS.value:
            character.msg("|y祥瑞之气环绕，你感到心境平和。|n")
            
        elif self.feng_shui_type == FengShuiType.BLESSED.value:
            character.msg("|c这里是修炼福地，灵气充盈！|n")
            
        # 房间类型效果
        if self.room_type == RoomType.CULTIVATION.value:
            character.msg("|b这是专门的修炼室，在这里修炼效果更佳。|n")
            
    def check_random_event(self, character):
        """检查随机事件"""
        # 检查事件冷却
        if self.last_event_time:
            from datetime import datetime
            elapsed = (datetime.now() - self.last_event_time).total_seconds()
            if elapsed < self.event_cooldown:
                return
                
        # 随机事件概率
        event_chance = 0.1  # 10%基础概率
        
        # 特殊房间增加事件概率
        if self.room_type in [RoomType.WILDERNESS.value, RoomType.CAVE.value]:
            event_chance += 0.1
            
        if random.random() < event_chance:
            self.trigger_random_event(character)
            
    def trigger_random_event(self, character):
        """触发随机事件"""
        events = [
            "灵气波动",
            "宝物显现",
            "灵兽出现",
            "天象异常",
            "灵药生长"
        ]
        
        event = random.choice(events)
        self.last_event_time = datetime.now()
        
        if event == "灵气波动":
            self.spiritual_power += 50
            self.msg_contents("|c突然间，周围灵气波动，浓度有所提升！|n")
            
        elif event == "宝物显现":
            self.msg_contents("|y一道光芒闪过，似乎有宝物显现！|n")
            # 这里可以生成随机宝物
            
        elif event == "灵兽出现":
            self.msg_contents("|r远处传来兽吼声，有灵兽接近！|n")
            # 这里可以生成随机怪物
            
        elif event == "天象异常":
            self.msg_contents("|m天空出现异象，修炼者需要小心！|n")
            
        elif event == "灵药生长":
            self.msg_contents("|g地面长出了一株灵药！|n")
            # 这里可以生成灵药道具
            
    def get_cultivation_effectiveness(self, character):
        """获取在此房间修炼的效果"""
        base_effectiveness = 1.0
        
        # 房间修炼加成
        effectiveness = base_effectiveness * self.cultivation_bonus
        
        # 灵气浓度影响
        density_multiplier = {
            SpiritualDensity.BARREN.value: 0.5,
            SpiritualDensity.THIN.value: 0.8,
            SpiritualDensity.NORMAL.value: 1.0,
            SpiritualDensity.RICH.value: 1.3,
            SpiritualDensity.DENSE.value: 1.6,
            SpiritualDensity.OVERWHELMING.value: 2.0
        }.get(self.spiritual_density, 1.0)
        
        effectiveness *= density_multiplier
        
        return effectiveness
        
    def get_breakthrough_success_bonus(self):
        """获取突破成功率加成"""
        return self.breakthrough_bonus


# =============================================================================
# 特殊仙侠房间类型
# =============================================================================

class CultivationRoom(XianxiaRoom):
    """修炼室"""
    
    def at_object_creation(self):
        super().at_object_creation()
        self.room_type = RoomType.CULTIVATION.value
        self.cultivation_bonus = 2.0
        self.breakthrough_bonus = 20
        
        # 修炼室特有描述
        self.db.desc = (
            "这是一间专门用于修炼的静室。四壁刻满了静心咒文，"
            "地面铺设着聚灵阵法，空气中弥漫着浓郁的灵气。"
            "在这里修炼，效果会比平常好很多。"
        )


class AlchemyRoom(XianxiaRoom):
    """炼丹房"""
    
    def at_object_creation(self):
        super().at_object_creation()
        self.room_type = RoomType.ALCHEMY.value
        
        # 炼丹房特有描述
        self.db.desc = (
            "这是一间炼丹房。房间中央放置着一尊古朴的丹炉，"
            "四周摆放着各种炼丹工具和药材。空气中飘着淡淡的丹香，"
            "在这里炼丹成功率会有所提升。"
        )
        
        # 炼丹房特殊效果
        self.special_effects.append("炼丹成功率+20%")


class TreasureVault(XianxiaRoom):
    """宝库"""
    
    def at_object_creation(self):
        super().at_object_creation()
        self.room_type = RoomType.TREASURE.value
        
        # 宝库特有描述
        self.db.desc = (
            "这是一间宝库。四周的架子上摆放着各种珍贵的法宝和材料，"
            "金光闪闪，宝气逼人。强大的阵法保护着这里的宝物。"
        )
        
        # 宝库通常有守护
        self.special_effects.append("有强大的守护阵法")


class SectHall(XianxiaRoom):
    """宗门大殿"""
    
    def at_object_creation(self):
        super().at_object_creation()
        self.room_type = RoomType.SECT_HALL.value
        self.spiritual_density = SpiritualDensity.RICH.value
        self.feng_shui_type = FengShuiType.AUSPICIOUS.value
        
        # 宗门大殿特有描述
        self.db.desc = (
            "这是宗门的大殿。殿堂宏伟庄严，正中供奉着祖师画像，"
            "两侧悬挂着历代宗主的画像。整个大殿散发着威严的气息。"
        )


class Wilderness(XianxiaRoom):
    """野外区域"""
    
    def at_object_creation(self):
        super().at_object_creation()
        self.room_type = RoomType.WILDERNESS.value
        
        # 增加随机事件概率
        self.event_cooldown = 1800  # 30分钟冷却
        
        # 野外描述模板
        wilderness_descs = [
            "这是一片荒野。远山如黛，野草丛生，偶尔能听到野兽的嚎叫声。",
            "这是一片竹林。青竹摇曳，竹叶沙沙作响，空气清新怡人。",
            "这是一片山谷。两侧山峰耸立，谷中溪水潺潺，鸟语花香。",
            "这是一片密林。古树参天，藤蔓缠绕，林中时有灵兽出没。"
        ]
        
        self.db.desc = random.choice(wilderness_descs)


# =============================================================================
# 仙侠副本房间
# =============================================================================

class XianxiaDungeonRoom(EvAdventureDungeonRoom, XianxiaRoom):
    """
    仙侠副本房间
    结合了副本系统和仙侠房间特色
    """
    
    def at_object_creation(self):
        """副本房间创建时初始化"""
        # 先调用副本房间的初始化
        EvAdventureDungeonRoom.at_object_creation(self)
        
        # 再调用仙侠房间的初始化
        XianxiaRoom.at_object_creation(self)
        
        # 副本房间特殊设置
        self.tags.add("xianxia_dungeon_room", category="room_type")
        
    def get_display_header(self, looker, **kwargs):
        """显示副本房间信息"""
        # 获取副本信息
        header = EvAdventureDungeonRoom.get_display_header(self, looker, **kwargs)
        
        # 添加仙侠信息
        xianxia_info = f"\n|c[副本-{self.room_type}] 灵气: {self.spiritual_density} | 风水: {self.feng_shui_type}|n"
        
        return header + xianxia_info
