<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}仙侠世界MUD{% endblock %}</title>
    
    {% load static %}

    <!-- 基础CSS -->
    <link rel="stylesheet" href="{% static 'webclient/css/webclient.css' %}">
    <link rel="stylesheet" href="{% static 'webclient/css/goldenlayout.css' %}">

    <!-- 仙侠主题CSS -->
    <link rel="stylesheet" href="{% static 'webclient/css/xianxia.css' %}">
    
    <!-- GoldenLayout CSS -->
    <link rel="stylesheet" href="https://golden-layout.com/files/latest/css/goldenlayout-base.css">
    <link rel="stylesheet" href="https://golden-layout.com/files/latest/css/goldenlayout-dark-theme.css">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- GoldenLayout JS -->
    <script src="https://golden-layout.com/files/latest/js/goldenlayout.min.js"></script>
    
    {% block extra_head %}{% endblock %}
</head>

<body>
    <div id="clientwrapper" class="wrapper">
        <div id="main">
            <div id="main-sub">
                <!-- 默认消息窗口 - 将被GoldenLayout替换 -->
                <div id="messagewindow" class="content"></div>
                
                <!-- 默认输入区域 - 将被GoldenLayout替换 -->
                <div id="inputcontrol">
                    <div id="prompt">></div>
                    <input type="text" id="inputfield" class="inputfield" autocomplete="off" />
                    <button id="inputsend">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 选项对话框 -->
    <div id="optionsdialog" class="dialog">
        <div class="dialogtitle">
            <span>设置选项</span>
            <span class="dialogclose" onclick="$('#optionsdialog').hide();">×</span>
        </div>
        <div class="dialogcontentparent">
            <div class="dialogcontent" id="optionsdialogcontent">
                <!-- 选项内容将由插件动态生成 -->
            </div>
        </div>
        <div class="dialogfooter">
            <button onclick="$('#optionsdialog').hide();">关闭</button>
        </div>
    </div>

    <!-- 帮助对话框 -->
    <div id="helpdialog" class="dialog">
        <div class="dialogtitle">
            <span>帮助信息</span>
            <span class="dialogclose" onclick="$('#helpdialog').hide();">×</span>
        </div>
        <div class="dialogcontentparent">
            <div class="dialogcontent" id="helpdialogcontent">
                <h3>仙侠世界MUD 帮助</h3>
                <p>欢迎来到仙侠世界！这是一个基于Evennia的仙侠主题MUD游戏。</p>
                
                <h4>基础命令：</h4>
                <ul>
                    <li><strong>状态</strong> - 查看角色修炼状态</li>
                    <li><strong>修炼</strong> - 开始修炼提升修为</li>
                    <li><strong>突破</strong> - 尝试突破境界</li>
                    <li><strong>技能</strong> - 查看已学技能</li>
                    <li><strong>法宝</strong> - 查看拥有的法宝</li>
                    <li><strong>加入门派 [门派名]</strong> - 加入指定门派</li>
                </ul>
                
                <h4>快捷键：</h4>
                <ul>
                    <li><strong>Ctrl + 1-9</strong> - 执行快捷命令</li>
                    <li><strong>上/下箭头</strong> - 浏览命令历史</li>
                    <li><strong>Tab</strong> - 命令自动补全</li>
                </ul>
                
                <h4>界面说明：</h4>
                <ul>
                    <li><strong>左侧</strong> - 主游戏窗口和命令输入</li>
                    <li><strong>右上</strong> - 角色状态面板</li>
                    <li><strong>右下</strong> - 快捷命令栏</li>
                </ul>
            </div>
        </div>
        <div class="dialogfooter">
            <button onclick="$('#helpdialog').hide();">关闭</button>
        </div>
    </div>

    <!-- 核心JavaScript文件 -->
    <script src="{% static 'webclient/js/evennia.js' %}"></script>
    <script src="{% static 'webclient/js/webclient_gui.js' %}"></script>

    <!-- 仙侠布局配置 - 必须在goldenlayout.js之前加载 -->
    <script src="{% static 'webclient/js/plugins/goldenlayout_xianxia_config.js' %}"></script>

    <!-- 插件系统 -->
    <script src="{% static 'webclient/js/plugins/default_in.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/default_out.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/goldenlayout.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/history.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/options.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/notifications.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/html.js' %}"></script>

    <!-- 仙侠专用插件 -->
    <script src="{% static 'webclient/js/plugins/xianxia_stats.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/xianxia_hotbar.js' %}"></script>
    <script src="{% static 'webclient/js/plugins/xianxia_ui_enhancer.js' %}"></script>
    
    <!-- 自定义初始化脚本 -->
    <script>
        $(document).ready(function() {
            // 设置页面标题
            document.title = "仙侠世界MUD - 踏上修仙之路";
            
            // 添加仙侠风格的欢迎消息
            setTimeout(function() {
                if (window.plugin_handler && window.plugin_handler.onText) {
                    const welcomeMsg = `
<div style="text-align: center; color: #FFD700; font-size: 16px; margin: 20px 0; padding: 15px; border: 2px solid #8B4513; border-radius: 8px; background: rgba(139, 69, 19, 0.2);">
    <div style="font-size: 20px; font-weight: bold; margin-bottom: 10px;">🏮 欢迎来到仙侠世界 🏮</div>
    <div>踏上修仙之路，问鼎仙道巅峰！</div>
    <div style="font-size: 12px; margin-top: 10px; color: #F5DEB3;">
        输入 <span style="color: #FFD700; font-weight: bold;">状态</span> 查看角色信息 | 
        输入 <span style="color: #FFD700; font-weight: bold;">修炼</span> 开始修炼 | 
        点击右侧快捷按钮快速操作
    </div>
</div>
                    `;
                    window.plugin_handler.onText([welcomeMsg], {cls: 'system-msg'});
                }
            }, 2000);
            
            // 添加键盘快捷键提示
            $(document).on('keydown', function(e) {
                // F1 显示帮助
                if (e.keyCode === 112) {
                    $('#helpdialog').show();
                    e.preventDefault();
                }
                // F2 显示设置
                else if (e.keyCode === 113) {
                    $('#optionsdialog').show();
                    e.preventDefault();
                }
            });
            
            // 添加右键菜单（可选）
            $(document).on('contextmenu', '#messagewindow', function(e) {
                e.preventDefault();
                // 可以在这里添加右键菜单功能
                return false;
            });
            
            console.log('仙侠MUD界面初始化完成');
        });
        
        // 全局函数：显示帮助
        function showHelp() {
            $('#helpdialog').show();
        }
        
        // 全局函数：显示设置
        function showOptions() {
            $('#optionsdialog').show();
        }
        
        // 全局函数：执行命令
        function executeCommand(cmd) {
            if (window.Evennia && window.Evennia.msg) {
                window.Evennia.msg("text", [cmd], {});
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
