# 🏮 仙侠MUD第一阶段使用说明

## 📋 系统概述

第一阶段实现了仙侠MUD的核心基础功能：
- ✅ **仙侠角色系统** - 境界、灵力、门派、技能
- ✅ **法宝道具系统** - 法宝、武器、丹药、灵石
- ✅ **仙侠房间系统** - 灵气、风水、修炼加成
- ✅ **仙侠命令系统** - 修炼、突破、法宝操作等

## 🚀 安装和配置

### 1. 文件部署
将以下文件放入Evennia项目的 `typeclasses` 目录：
```
typeclasses/
├── xianxia/
│   ├── __init__.py
│   ├── characters.py
│   ├── objects.py
│   ├── rooms.py
│   └── commands.py
└── test_xianxia_system.py
```

### 2. 配置设置
在 `server/conf/settings.py` 中添加：
```python
# 仙侠角色类型类
BASE_CHARACTER_TYPECLASS = "typeclasses.xianxia.characters.XianxiaCharacter"

# 仙侠房间类型类  
BASE_ROOM_TYPECLASS = "typeclasses.xianxia.rooms.XianxiaRoom"

# 添加仙侠命令集
CMDSET_CHARACTER = "typeclasses.xianxia.commands.XianxiaCmdSet"
```

### 3. 运行测试
在游戏中执行测试脚本：
```
@py exec(open('test_xianxia_system.py').read())
```

## 🎮 游戏功能使用指南

### 🧙‍♂️ 角色系统

#### 创建仙侠角色
```python
# 在游戏中创建角色
@create/drop typeclasses.xianxia.characters.XianxiaCharacter:我的修士
```

#### 查看角色状态
```
状态          # 查看详细修炼状态
技能          # 查看技能列表
仙侠帮助      # 查看所有仙侠命令
```

#### 境界系统
- **境界等级**: 凡人 → 练气期 → 筑基期 → 金丹期 → 元婴期 → 化神期 → 炼虚期 → 合体期 → 大乘期 → 渡劫期 → 仙人
- **境界层次**: 每个境界分为1-9层
- **突破机制**: 修炼经验满足后可尝试突破

### 🧘‍♂️ 修炼系统

#### 基础修炼
```
修炼          # 默认修炼60分钟
修炼 120      # 修炼120分钟
停止修炼      # 停止当前修炼
```

#### 境界突破
```
突破          # 尝试突破到下一层
```

**突破条件**：
- 修炼经验达到要求
- 不在修炼状态中
- 不在突破过程中

**突破风险**：
- 基础风险20%
- 境界越高风险越大
- 技能等级可降低风险
- 失败会走火入魔

### 🗡️ 法宝系统

#### 创建法宝
```python
# 创建基础法宝
@create typeclasses.xianxia.objects.XianxiaTreasure:测试法宝

# 创建武器
@create typeclasses.xianxia.objects.XianxiaWeapon:青锋剑

# 设置武器属性
@set 青锋剑/weapon_type = 剑
@set 青锋剑/treasure_grade = 灵品
```

#### 法宝操作
```
法宝          # 查看拥有的法宝
认主 青锋剑   # 认主法宝
提升认主 青锋剑  # 提升认主程度
升级法宝 青锋剑  # 升级法宝等级
```

#### 法宝品级系统
- **凡品** - 普通法宝，威力1.0倍
- **灵品** - 灵性法宝，威力1.5倍  
- **宝品** - 珍贵法宝，威力2.0倍
- **仙品** - 仙家法宝，威力3.0倍
- **神品** - 神级法宝，威力5.0倍

### 💊 丹药系统

#### 创建丹药
```python
# 创建疗伤丹
@create typeclasses.xianxia.objects.XianxiaPill:疗伤丹
@set 疗伤丹/pill_type = 疗伤丹
@set 疗伤丹/pill_grade = 中品

# 创建回灵丹
@create typeclasses.xianxia.objects.XianxiaPill:回灵丹  
@set 回灵丹/pill_type = 回灵丹
```

#### 使用丹药
```
服用 疗伤丹   # 服用疗伤丹恢复生命值
服用 回灵丹   # 服用回灵丹恢复灵力
```

#### 丹药类型
- **疗伤丹** - 恢复生命值
- **回灵丹** - 恢复灵力
- **破境丹** - 增加突破成功率
- **解毒丹** - 解除中毒状态
- **力量丹** - 临时增加力量

### 💎 灵石系统

#### 创建灵石
```python
# 创建不同品级的灵石
@create typeclasses.xianxia.objects.SpiritStone:下品灵石
@create typeclasses.xianxia.objects.SpiritStone:中品灵石
@set 中品灵石/stone_grade = 中品
```

#### 使用灵石
```
吸收 下品灵石  # 吸收灵石恢复灵力
```

#### 灵石品级
- **下品灵石** - 价值1，恢复1点灵力
- **中品灵石** - 价值10，恢复10点灵力
- **上品灵石** - 价值100，恢复100点灵力
- **极品灵石** - 价值1000，恢复1000点灵力

### 🏠 房间系统

#### 创建仙侠房间
```python
# 创建普通房间
@create typeclasses.xianxia.rooms.XianxiaRoom:测试房间

# 创建修炼室
@create typeclasses.xianxia.rooms.CultivationRoom:修炼室

# 创建炼丹房
@create typeclasses.xianxia.rooms.AlchemyRoom:炼丹房
```

#### 房间属性
- **灵气浓度** - 影响修炼效果
  - 贫瘠(0.5倍) → 稀薄(0.8倍) → 普通(1.0倍) → 浓郁(1.3倍) → 浓密(1.6倍) → 充盈(2.0倍)

- **风水类型** - 影响修炼和突破
  - 普通：无加成
  - 聚灵：修炼+50%，突破+10%
  - 煞气：修炼-30%，突破-20%
  - 祥瑞：修炼+30%，突破+15%
  - 凶险：修炼-50%，突破-30%
  - 福地：修炼+100%，突破+25%

### 🏛️ 门派系统

#### 门派操作
```
加入门派 剑宗    # 加入剑宗
离开门派        # 离开当前门派
```

#### 可用门派
- **散修** - 无门派加成
- **剑宗** - 剑法修炼加成
- **丹宗** - 炼丹成功率加成
- **阵法门** - 阵法威力加成
- **御兽门** - 驯兽能力加成
- **魔道宗** - 魔功修炼加成
- **佛门** - 佛法修炼加成
- **符箓派** - 符箓制作加成

## 🎯 游戏流程示例

### 新手入门流程
1. **创建角色** - 成为练气期1层修士
2. **查看状态** - 了解当前境界和属性
3. **开始修炼** - 积累修炼经验
4. **获得法宝** - 认主并提升认主程度
5. **尝试突破** - 提升境界层次
6. **加入门派** - 获得门派加成
7. **探索世界** - 寻找更好的修炼地点

### 进阶玩法
1. **法宝升级** - 提升法宝等级和品级
2. **技能修炼** - 学习和提升各种技能
3. **丹药炼制** - 制作各种功效的丹药
4. **房间选择** - 寻找最佳修炼环境
5. **风险管理** - 平衡修炼速度和突破风险

## 🔧 管理员命令

### 角色管理
```python
# 设置角色境界
@set 角色名/realm = 筑基期
@set 角色名/realm_level = 5

# 设置灵力
@set 角色名/spiritual_power = 500
@set 角色名/spiritual_power_max = 500

# 添加修炼经验
@set 角色名/cultivation_exp = 2000
```

### 道具创建
```python
# 创建高级法宝
@create typeclasses.xianxia.objects.XianxiaWeapon:仙剑
@set 仙剑/treasure_grade = 仙品
@set 仙剑/treasure_level = 9

# 批量创建丹药
@create typeclasses.xianxia.objects.XianxiaPill:极品疗伤丹
@set 极品疗伤丹/pill_grade = 极品
```

### 房间配置
```python
# 设置房间属性
@set 房间名/spiritual_density = 充盈
@set 房间名/feng_shui_type = 福地
@set 房间名/cultivation_bonus = 3.0
```

## 🐛 故障排除

### 常见问题

**Q: 角色无法修炼？**
A: 检查是否已经在修炼中或突破中，使用"停止修炼"命令

**Q: 法宝无法认主？**
A: 检查修为是否足够，或法宝是否已有主人

**Q: 突破总是失败？**
A: 提升打坐和内功技能等级，选择风水好的房间

**Q: 命令无法使用？**
A: 确认已正确配置命令集，重新登录游戏

### 调试命令
```python
# 检查角色属性
@examine 角色名

# 检查对象类型
@typeclass 对象名

# 重新加载代码
@reload
```

## 📈 下一阶段预告

第二阶段将实现：
- 🗡️ **随机敌人系统** - 动态生成和刷新
- 📜 **任务系统** - 各种类型的任务
- ⚔️ **战斗系统优化** - 仙侠特色战斗
- 🏰 **副本系统** - 随机生成的仙侠副本

敬请期待！🌟
