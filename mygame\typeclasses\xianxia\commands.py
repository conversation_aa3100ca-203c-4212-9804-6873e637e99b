"""
仙侠命令系统
为仙侠MUD提供专门的命令集

包含命令：
- 修炼相关：修炼(xiulian)、突破(tupo)、停止修炼(stop)
- 状态查看：状态(status)、技能(skills)、法宝(fabao)
- 法宝操作：认主(renzhu)、升级法宝(upgrade)
- 丹药使用：服用(fuyong)
- 门派操作：加入门派(join)、离开门派(leave)
"""

from evennia import Command, CmdSet
from evennia.utils.evtable import EvTable


# =============================================================================
# 修炼相关命令
# =============================================================================

class CmdCultivate(Command):
    """
    修炼命令
    
    用法:
        修炼 [时间]
        xiulian [minutes]
        
    开始修炼，提升修为。可以指定修炼时间（分钟），默认60分钟。
    """
    
    key = "修炼"
    aliases = ["xiulian", "cultivate"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        # 检查是否为仙侠角色
        if not hasattr(caller, 'realm'):
            caller.msg("只有修仙者才能修炼。")
            return
            
        # 解析参数
        duration = 60  # 默认60分钟
        if self.args.strip():
            try:
                duration = int(self.args.strip())
                if duration <= 0 or duration > 480:  # 最多8小时
                    caller.msg("修炼时间必须在1-480分钟之间。")
                    return
            except ValueError:
                caller.msg("请输入有效的修炼时间（分钟）。")
                return
                
        # 开始修炼
        if caller.start_cultivation(duration):
            caller.msg(f"开始修炼{duration}分钟...")
        else:
            caller.msg("无法开始修炼。")


class CmdBreakthrough(Command):
    """
    突破命令
    
    用法:
        突破
        tupo
        breakthrough
        
    尝试突破到下一个境界层次。
    """
    
    key = "突破"
    aliases = ["tupo", "breakthrough"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'realm'):
            caller.msg("只有修仙者才能突破。")
            return
            
        # 尝试突破
        caller.start_breakthrough()


class CmdStopCultivation(Command):
    """
    停止修炼命令
    
    用法:
        停止修炼
        stop
        
    停止当前的修炼。
    """
    
    key = "停止修炼"
    aliases = ["stop", "停止"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'is_cultivating'):
            caller.msg("你没有在修炼。")
            return
            
        if caller.stop_cultivation():
            caller.msg("停止修炼。")


# =============================================================================
# 状态查看命令
# =============================================================================

class CmdStatus(Command):
    """
    状态命令
    
    用法:
        状态
        status
        
    查看角色的详细状态信息。
    """
    
    key = "状态"
    aliases = ["status", "zhuangtai"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'realm'):
            caller.msg("你不是修仙者。")
            return
            
        # 显示状态信息
        status_text = caller.get_status_display()
        caller.msg(f"|c=== 修仙状态 ===|n\n{status_text}")


class CmdSkills(Command):
    """
    技能命令
    
    用法:
        技能
        skills
        
    查看角色的技能信息。
    """
    
    key = "技能"
    aliases = ["skills", "jineng"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'skills'):
            caller.msg("你没有学会任何技能。")
            return
            
        # 显示技能信息
        skills_text = caller.get_skills_display()
        caller.msg(f"|c=== 技能列表 ===|n\n{skills_text}")


class CmdTreasures(Command):
    """
    法宝命令
    
    用法:
        法宝
        fabao
        treasures
        
    查看拥有的法宝信息。
    """
    
    key = "法宝"
    aliases = ["fabao", "treasures"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        # 查找拥有的法宝
        treasures = [obj for obj in caller.contents 
                    if obj.tags.get("xianxia_treasure", category="item_type")]
        
        if not treasures:
            caller.msg("你没有拥有任何法宝。")
            return
            
        # 创建法宝列表表格
        table = EvTable("法宝名称", "品级", "等级", "认主程度", border="cells")
        
        for treasure in treasures:
            recognition = "未认主"
            if hasattr(treasure, 'owner_id') and treasure.owner_id == caller.id:
                recognition = f"{treasure.recognition_level}/10"
                
            table.add_row(
                treasure.name,
                treasure.treasure_grade,
                treasure.treasure_level,
                recognition
            )
            
        caller.msg(f"|c=== 法宝列表 ===|n\n{table}")


# =============================================================================
# 法宝操作命令
# =============================================================================

class CmdRecognizeMaster(Command):
    """
    认主命令
    
    用法:
        认主 <法宝名称>
        renzhu <treasure_name>
        
    尝试认主指定的法宝。
    """
    
    key = "认主"
    aliases = ["renzhu", "recognize"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not self.args:
            caller.msg("用法：认主 <法宝名称>")
            return
            
        # 查找法宝
        treasure_name = self.args.strip()
        treasure = caller.search(treasure_name, candidates=caller.contents)
        
        if not treasure:
            return
            
        # 检查是否为法宝
        if not treasure.tags.get("xianxia_treasure", category="item_type"):
            caller.msg("这不是法宝。")
            return
            
        # 尝试认主
        treasure.recognize_master(caller)


class CmdUpgradeTreasure(Command):
    """
    升级法宝命令
    
    用法:
        升级法宝 <法宝名称>
        upgrade <treasure_name>
        
    升级指定的法宝。
    """
    
    key = "升级法宝"
    aliases = ["upgrade", "shengji"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not self.args:
            caller.msg("用法：升级法宝 <法宝名称>")
            return
            
        # 查找法宝
        treasure_name = self.args.strip()
        treasure = caller.search(treasure_name, candidates=caller.contents)
        
        if not treasure:
            return
            
        # 检查是否为法宝
        if not treasure.tags.get("xianxia_treasure", category="item_type"):
            caller.msg("这不是法宝。")
            return
            
        # 检查是否为主人
        if not hasattr(treasure, 'owner_id') or treasure.owner_id != caller.id:
            caller.msg("只有法宝主人才能升级法宝。")
            return
            
        # 尝试升级
        success, message = treasure.upgrade_level()
        caller.msg(message)


class CmdImproveBond(Command):
    """
    提升认主命令
    
    用法:
        提升认主 <法宝名称>
        improve <treasure_name>
        
    提升与法宝的认主程度。
    """
    
    key = "提升认主"
    aliases = ["improve", "tisheng"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not self.args:
            caller.msg("用法：提升认主 <法宝名称>")
            return
            
        # 查找法宝
        treasure_name = self.args.strip()
        treasure = caller.search(treasure_name, candidates=caller.contents)
        
        if not treasure:
            return
            
        # 检查是否为法宝
        if not treasure.tags.get("xianxia_treasure", category="item_type"):
            caller.msg("这不是法宝。")
            return
            
        # 尝试提升认主程度
        treasure.improve_recognition(caller)


# =============================================================================
# 丹药使用命令
# =============================================================================

class CmdConsumePill(Command):
    """
    服用丹药命令
    
    用法:
        服用 <丹药名称>
        fuyong <pill_name>
        consume <pill_name>
        
    服用指定的丹药。
    """
    
    key = "服用"
    aliases = ["fuyong", "consume", "use"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not self.args:
            caller.msg("用法：服用 <丹药名称>")
            return
            
        # 查找丹药
        pill_name = self.args.strip()
        pill = caller.search(pill_name, candidates=caller.contents)
        
        if not pill:
            return
            
        # 检查是否为丹药
        if not pill.tags.get("xianxia_pill", category="item_type"):
            caller.msg("这不是丹药。")
            return
            
        # 使用丹药
        if pill.use(caller):
            caller.msg(f"服用了{pill.name}。")
        else:
            caller.msg("无法服用这个丹药。")


# =============================================================================
# 门派操作命令
# =============================================================================

class CmdJoinSect(Command):
    """
    加入门派命令
    
    用法:
        加入门派 <门派名称>
        join <sect_name>
        
    加入指定的门派。
    """
    
    key = "加入门派"
    aliases = ["join", "jiaru"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'sect'):
            caller.msg("你不是修仙者。")
            return
            
        if not self.args:
            caller.msg("用法：加入门派 <门派名称>")
            return
            
        sect_name = self.args.strip()
        
        # 这里应该检查门派是否存在，简化版本直接加入
        if caller.join_sect(sect_name):
            caller.msg(f"成功加入{sect_name}！")
        else:
            caller.msg("加入门派失败。")


class CmdLeaveSect(Command):
    """
    离开门派命令
    
    用法:
        离开门派
        leave
        
    离开当前门派。
    """
    
    key = "离开门派"
    aliases = ["leave", "likai"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not hasattr(caller, 'sect'):
            caller.msg("你不是修仙者。")
            return
            
        if caller.leave_sect():
            caller.msg("离开门派成功。")
        else:
            caller.msg("离开门派失败。")


# =============================================================================
# 灵石操作命令
# =============================================================================

class CmdAbsorbStone(Command):
    """
    吸收灵石命令
    
    用法:
        吸收 <灵石>
        absorb <spirit_stone>
        
    吸收灵石恢复灵力。
    """
    
    key = "吸收"
    aliases = ["absorb", "xishou"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        if not self.args:
            caller.msg("用法：吸收 <灵石名称>")
            return
            
        # 查找灵石
        stone_name = self.args.strip()
        stone = caller.search(stone_name, candidates=caller.contents)
        
        if not stone:
            return
            
        # 检查是否为灵石
        if not stone.tags.get("spirit_stone", category="item_type"):
            caller.msg("这不是灵石。")
            return
            
        # 吸收灵石
        stone.absorb_energy(caller)


# =============================================================================
# 命令集
# =============================================================================

class XianxiaCmdSet(CmdSet):
    """
    仙侠命令集
    包含所有仙侠相关的命令
    """
    
    key = "XianxiaCmdSet"
    
    def at_cmdset_creation(self):
        """创建命令集时添加所有命令"""
        
        # 修炼相关命令
        self.add(CmdCultivate())
        self.add(CmdBreakthrough())
        self.add(CmdStopCultivation())
        
        # 状态查看命令
        self.add(CmdStatus())
        self.add(CmdSkills())
        self.add(CmdTreasures())
        
        # 法宝操作命令
        self.add(CmdRecognizeMaster())
        self.add(CmdUpgradeTreasure())
        self.add(CmdImproveBond())
        
        # 丹药使用命令
        self.add(CmdConsumePill())
        
        # 门派操作命令
        self.add(CmdJoinSect())
        self.add(CmdLeaveSect())
        
        # 灵石操作命令
        self.add(CmdAbsorbStone())


# =============================================================================
# 帮助命令扩展
# =============================================================================

class CmdXianxiaHelp(Command):
    """
    仙侠帮助命令
    
    用法:
        仙侠帮助
        xianxia_help
        
    显示所有仙侠相关命令的帮助。
    """
    
    key = "仙侠帮助"
    aliases = ["xianxia_help", "仙侠"]
    locks = "cmd:all()"
    help_category = "仙侠"
    
    def func(self):
        caller = self.caller
        
        help_text = """
|c=== 仙侠世界命令帮助 ===|n

|y修炼相关：|n
  修炼 [时间]     - 开始修炼，提升修为
  突破           - 尝试突破境界
  停止修炼       - 停止当前修炼

|y状态查看：|n
  状态           - 查看角色状态
  技能           - 查看技能列表
  法宝           - 查看拥有的法宝

|y法宝操作：|n
  认主 <法宝>    - 认主法宝
  升级法宝 <法宝> - 升级法宝等级
  提升认主 <法宝> - 提升认主程度

|y丹药使用：|n
  服用 <丹药>    - 服用丹药

|y门派操作：|n
  加入门派 <门派> - 加入门派
  离开门派       - 离开当前门派

|y灵石操作：|n
  吸收 <灵石>    - 吸收灵石恢复灵力

|g使用 '帮助 <命令名>' 查看具体命令的详细说明。|n
        """
        
        caller.msg(help_text)
