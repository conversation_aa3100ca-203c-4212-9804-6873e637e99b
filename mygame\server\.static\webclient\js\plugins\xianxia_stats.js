/*
 * 仙侠状态面板插件
 * 显示角色的修炼状态、境界、门派等信息
 */

let xianxia_stats_plugin = (function () {
    
    // 当前角色数据
    let characterData = {
        realm: "练气期",
        realm_level: 1,
        spiritual_power: 100,
        spiritual_power_max: 100,
        cultivation_exp: 0,
        cultivation_exp_needed: 1000,
        sect: "散修",
        sect_rank: "外门弟子",
        health: 100,
        health_max: 100,
        status: "正常"
    };
    
    // 创建状态面板HTML
    function createStatsPanel() {
        return `
            <div class="xianxia-stats spiritual-aura">
                <h3>修仙状态</h3>
                
                <!-- 境界显示 -->
                <div class="realm-display">
                    <div class="realm-name" id="realm-display">${characterData.realm}</div>
                    <div class="realm-level" id="realm-level">${characterData.realm_level}层</div>
                </div>
                
                <!-- 门派信息 -->
                <div class="sect-info">
                    <div class="sect-name" id="sect-name">${characterData.sect}</div>
                    <div class="sect-rank" id="sect-rank">${characterData.sect_rank}</div>
                </div>
                
                <!-- 生命值 -->
                <div class="stat-bar health-bar">
                    <div class="stat-label">
                        <span>生命值</span>
                        <span id="health-text">${characterData.health}/${characterData.health_max}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="health-fill" style="width: ${(characterData.health / characterData.health_max * 100)}%"></div>
                    </div>
                </div>
                
                <!-- 灵力 -->
                <div class="stat-bar spiritual-power">
                    <div class="stat-label">
                        <span>灵力</span>
                        <span id="sp-text">${characterData.spiritual_power}/${characterData.spiritual_power_max}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="sp-fill" style="width: ${(characterData.spiritual_power / characterData.spiritual_power_max * 100)}%"></div>
                    </div>
                </div>
                
                <!-- 修炼进度 -->
                <div class="stat-bar cultivation-progress">
                    <div class="stat-label">
                        <span>修炼进度</span>
                        <span id="cult-text">${characterData.cultivation_exp}/${characterData.cultivation_exp_needed}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="cult-fill" style="width: ${(characterData.cultivation_exp / characterData.cultivation_exp_needed * 100)}%"></div>
                    </div>
                </div>
                
                <!-- 状态 -->
                <div class="stat-bar">
                    <div class="stat-label">
                        <span>状态</span>
                        <span id="status-text" style="color: #98FB98;">${characterData.status}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 更新状态显示
    function updateStats(data) {
        if (data) {
            // 更新数据
            Object.assign(characterData, data);
        }
        
        // 更新境界显示
        $("#realm-display").text(characterData.realm);
        $("#realm-level").text(characterData.realm_level + "层");
        
        // 更新门派信息
        $("#sect-name").text(characterData.sect);
        $("#sect-rank").text(characterData.sect_rank);
        
        // 更新生命值
        const healthPercent = (characterData.health / characterData.health_max * 100);
        $("#health-text").text(`${characterData.health}/${characterData.health_max}`);
        $("#health-fill").css("width", healthPercent + "%");
        
        // 更新灵力
        const spPercent = (characterData.spiritual_power / characterData.spiritual_power_max * 100);
        $("#sp-text").text(`${characterData.spiritual_power}/${characterData.spiritual_power_max}`);
        $("#sp-fill").css("width", spPercent + "%");
        
        // 更新修炼进度
        const cultPercent = (characterData.cultivation_exp / characterData.cultivation_exp_needed * 100);
        $("#cult-text").text(`${characterData.cultivation_exp}/${characterData.cultivation_exp_needed}`);
        $("#cult-fill").css("width", cultPercent + "%");
        
        // 更新状态
        $("#status-text").text(characterData.status);
        
        // 如果修炼经验满了，添加发光效果
        if (cultPercent >= 100) {
            $("#cult-fill").parent().addClass("breakthrough-glow");
        } else {
            $("#cult-fill").parent().removeClass("breakthrough-glow");
        }
    }
    
    // 解析游戏消息中的状态信息
    function parseStatusMessage(message) {
        // 解析 "=== 修仙状态 ===" 格式的消息
        if (message.includes("=== 修仙状态 ===")) {
            const lines = message.split('\n');
            const newData = {};
            
            lines.forEach(line => {
                line = line.trim();
                if (line.includes("境界：")) {
                    const match = line.match(/境界：(.+?)(\d+)层/);
                    if (match) {
                        newData.realm = match[1];
                        newData.realm_level = parseInt(match[2]);
                    }
                } else if (line.includes("门派：")) {
                    const match = line.match(/门派：(.+?)\s*\((.+?)\)/);
                    if (match) {
                        newData.sect = match[1];
                        newData.sect_rank = match[2];
                    }
                } else if (line.includes("灵力：")) {
                    const match = line.match(/灵力：(\d+)\/(\d+)/);
                    if (match) {
                        newData.spiritual_power = parseInt(match[1]);
                        newData.spiritual_power_max = parseInt(match[2]);
                    }
                } else if (line.includes("修炼进度：")) {
                    const match = line.match(/修炼进度：(\d+)\/(\d+)/);
                    if (match) {
                        newData.cultivation_exp = parseInt(match[1]);
                        newData.cultivation_exp_needed = parseInt(match[2]);
                    }
                } else if (line.includes("状态：")) {
                    const match = line.match(/状态：(.+)/);
                    if (match) {
                        newData.status = match[1];
                    }
                }
            });
            
            if (Object.keys(newData).length > 0) {
                updateStats(newData);
                return true;
            }
        }
        
        return false;
    }
    
    // 处理文本消息
    var onText = function (args, kwargs) {
        const message = args[0];
        
        // 尝试解析状态消息
        if (parseStatusMessage(message)) {
            return false; // 阻止消息显示在主窗口
        }
        
        // 检查修炼相关消息
        if (message.includes("修炼完成，获得") && message.includes("点修炼经验")) {
            const match = message.match(/获得(\d+)点修炼经验/);
            if (match) {
                const exp = parseInt(match[1]);
                characterData.cultivation_exp = Math.min(
                    characterData.cultivation_exp + exp, 
                    characterData.cultivation_exp_needed
                );
                updateStats();
            }
        }
        
        // 检查突破消息
        if (message.includes("突破成功") || message.includes("突破失败")) {
            // 添加特殊效果
            $(".xianxia-stats").addClass("breakthrough-glow");
            setTimeout(() => {
                $(".xianxia-stats").removeClass("breakthrough-glow");
            }, 3000);
        }
        
        return true; // 继续正常处理消息
    };
    
    // 注册组件到GoldenLayout
    var onLayoutChanged = function() {
        if (window.myLayout) {
            window.myLayout.registerComponent('xianxia_stats', function(container, componentState) {
                container.getElement().html(createStatsPanel());
                
                // 定期更新显示
                setInterval(updateStats, 1000);
            });
        }
    };
    
    // 初始化插件
    var init = function () {
        console.log('仙侠状态面板插件已加载');
        
        // 如果GoldenLayout已经初始化，立即注册组件
        if (window.myLayout) {
            onLayoutChanged();
        }
    };
    
    // 手动更新状态（供其他插件调用）
    var updateCharacterStats = function(data) {
        updateStats(data);
    };
    
    // 获取当前状态数据
    var getCharacterData = function() {
        return characterData;
    };
    
    return {
        init: init,
        onText: onText,
        onLayoutChanged: onLayoutChanged,
        updateCharacterStats: updateCharacterStats,
        getCharacterData: getCharacterData
    };
})();

// 注册插件
window.plugin_handler.add('xianxia_stats', xianxia_stats_plugin);
